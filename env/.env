VITE_APP_TITLE = 'Glowxq-Rescue'
VITE_APP_PORT = 9000

VITE_UNI_APPID = '__UNI__D1E5001'
VITE_WX_APPID = 'wx9c2218ac66f654a4'

# h5部署网站的base，配置到 manifest.config.ts 里的 h5.router.base
VITE_APP_PUBLIC_BASE=/

# 登录页面
VITE_LOGIN_URL = '/pages/login/index'

VITE_SERVER_BASEURL = 'http://localhost:7103/api'
VITE_SOCKET_URL=ws://glowxq.com:7203/api/socket
VITE_UPLOAD_BASEURL = 'https://ukw0y1.laf.run/upload'

# 有些同学可能需要在微信小程序里面根据 develop、trial、release 分别设置上传地址，参考代码如下。
# 下面的变量如果没有设置，会默认使用 VITE_SERVER_BASEURL or VITE_UPLOAD_BASEURL
VITE_SERVER_BASEURL__WEIXIN_DEVELOP = 'http://localhost:7103/api'
VITE_SERVER_BASEURL__WEIXIN_TRIAL = 'http://localhost:7103/api'
VITE_SERVER_BASEURL__WEIXIN_RELEASE = 'http://localhost:7103/api'

# h5是否需要配置代理
VITE_APP_PROXY=false
VITE_APP_PROXY_PREFIX = '/api'

# 高德地图API Key（Web端JS API类型）
VITE_AMAP_KEY=aaa0734a4c2c42b274a97a3ef2bdc430

# 高德地图安全密钥（securityJsCode）
VITE_AMAP_SECURITY_KEY=e14d867ad90d8f5bec6d5c7f6716bd33

VITE_APP_CLIENT_ID ="195da9fcce574852b850068771cde034"
# 是否对admin（超管）用户放行前端按钮权限验证，默认放行
VITE_ADMIN_BYPASS_PERMISSION=true
