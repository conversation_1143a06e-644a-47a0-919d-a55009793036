# Meta Text 数据库数据说明

## 概述

本目录包含了TextShow组件所需的所有数据库数据，用于替换Mock数据，实现真实的后端数据获取。

## 文件说明

### 1. `complete_meta_text_data.sql` - 完整数据脚本 ⭐ **推荐使用**
包含所有18条记录的完整SQL插入脚本，一次性插入所有数据。

**包含数据：**
- 关于我们页面：6条记录 (app-intro, company-info, version-info, contact-us, privacy-policy, terms-service)
- 帮助页面：12条记录 (常见问题3条 + 账号相关3条 + 物资管理3条 + 应急处理3条)

### 2. 分类数据文件（可选）
- `about_page_data.sql` - 仅关于我们页面数据（6条）
- `help_page_data.sql` - 仅帮助页面基础数据（6条）
- `materials_emergency_data.sql` - 物资管理和应急处理数据（6条）

## 快速执行

### 方式一：执行完整脚本（推荐）
```sql
-- 在MySQL中执行
source /path/to/complete_meta_text_data.sql;
```

### 方式二：直接复制粘贴
1. 打开 `complete_meta_text_data.sql` 文件
2. 复制所有SQL语句
3. 在MySQL客户端中粘贴执行

## 数据结构

### 关于我们页面数据映射

| text_key | 页面显示 | 业务类型 | 说明 |
|----------|----------|----------|------|
| app-intro | 应用介绍 | Information | 应用功能介绍 |
| company-info | 公司信息 | Cell | 公司基本信息 |
| version-info | 版本信息 | Announcement | 版本更新说明 |
| contact-us | 联系我们 | Cell | 客服联系方式 |
| privacy-policy | 隐私政策 | Agreement | 隐私保护政策 |
| terms-service | 服务条款 | Agreement | 使用条款 |

### 帮助页面数据映射

#### 常见问题分类
| text_key | 页面显示 | 说明 |
|----------|----------|------|
| how-to-use | 如何使用应用 | 基本使用指南 |
| feature-intro | 功能介绍 | 系统功能说明 |
| quick-start | 快速入门 | 新手入门指南 |

#### 账号相关分类
| text_key | 页面显示 | 说明 |
|----------|----------|------|
| login-help | 登录问题 | 登录故障排除 |
| profile-edit | 个人资料修改 | 资料修改指南 |
| password-reset | 密码重置 | 密码找回方法 |

#### 物资管理分类
| text_key | 页面显示 | 说明 |
|----------|----------|------|
| material-apply | 物资申请流程 | 申请操作指南 |
| material-review | 审批操作指南 | 审批流程说明 |
| material-track | 物资追踪 | 状态查询方法 |

#### 应急处理分类
| text_key | 页面显示 | 说明 |
|----------|----------|------|
| emergency-report | 紧急情况上报 | 紧急上报流程 |
| emergency-response | 应急响应流程 | 响应处理流程 |
| safety-guide | 安全操作指南 | 安全操作规范 |

## 验证数据

执行SQL后，可以使用以下查询验证数据：

```sql
-- 查看所有插入的数据
SELECT text_key, name, business_type, title, enable, sort 
FROM meta_text 
WHERE tenant_id = 'GLOWXQ' 
ORDER BY sort;

-- 统计记录数（应该是18条）
SELECT COUNT(*) as total_count FROM meta_text WHERE tenant_id = 'GLOWXQ';

-- 按业务类型分组统计
SELECT business_type, COUNT(*) as count 
FROM meta_text 
WHERE tenant_id = 'GLOWXQ' 
GROUP BY business_type;
```

## 注意事项

1. **租户ID**: 所有数据使用 `GLOWXQ` 作为租户ID
2. **唯一约束**: `text_key` 字段有唯一约束，重复执行前请先删除现有数据
3. **字符编码**: 确保数据库使用 `utf8mb4` 编码以支持emoji表情
4. **数据清理**: 如需重新插入，可先执行：
   ```sql
   DELETE FROM meta_text WHERE tenant_id = 'GLOWXQ';
   ```

## 前端代码更改

执行SQL后，前端代码已经做了以下更改：

1. ✅ **移除Mock数据**: 删除了 `src/mock/textShowData.ts`
2. ✅ **更新API模块**: `src/api/modules/system/meta/mateText.ts` 直接调用真实接口
3. ✅ **更新接口定义**: `src/api/interface/system/meta/mateText.ts` 匹配数据库字段
4. ✅ **页面正常工作**: 关于我们和帮助页面将从数据库获取真实数据

## 测试步骤

1. 执行SQL插入数据
2. 启动后端服务，确保 `/meta-text/getByKey` 接口正常
3. 启动前端应用
4. 访问"关于我们"页面，验证6个模块内容正常显示
5. 访问"帮助中心"页面，验证4个分类12个帮助项正常显示

## 联系支持

如果在执行过程中遇到问题，请检查：
- 数据库连接是否正常
- 表结构是否与SQL匹配
- 后端接口是否正常工作
- 前端网络请求是否成功
