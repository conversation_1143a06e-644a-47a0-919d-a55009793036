export namespace IMetaText {
  // 文本实体
  export interface MetaText {
    /** ID */
    id: number
    /** 文本名字 */
    name: string
    /** 文本key */
    textKey: string
    /** 文本类型 */
    textType: string
    /** 图标 */
    icon: string
    /** 业务类型 */
    businessType: string
    /** 文本标题 */
    title: string
    /** 跳转URL */
    skipUrl: string
    /** 文本内容 */
    content?: string
    /** 排序(降序) */
    sort: number
    /** 启用 */
    enable: boolean
    /** 创建时间 */
    createTime: string
    /** 更新时间 */
    updateTime: string
    /** 租户ID */
    tenantId: string
    /** 是否删除 */
    delFlag: string
    /** 创建人ID */
    createId?: number
    /** 更新人ID */
    updateId?: number
  }

  // 根据key获取文本的请求参数
  export interface GetByKeyParams {
    /** 文本key */
    key: string
  }
}
