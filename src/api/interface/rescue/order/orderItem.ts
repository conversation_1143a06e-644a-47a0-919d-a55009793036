import type { IPageQuery } from '@/api/interface'

export namespace IOrderItem {
  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    orderId?: number
    skuId?: number
    quantity?: number
    price?: number
    totalPrice?: number
  }

  // 新增表单
  export interface CreateForm {
    tenantId?: string
    deptId?: number
    orderId: number
    skuId: number
    quantity: number
    price: number
    totalPrice: number
  }

  // 编辑表单
  export interface UpdateForm {
    id: number
    tenantId?: string
    deptId?: number
    orderId: number
    skuId: number
    quantity: number
    price: number
    totalPrice: number
  }

  // 删除参数
  export interface DeleteParams {
    ids: number[]
  }

  // list或detail返回结构
  export interface Row {
    id: number
    tenantId?: string
    deptId?: number
    orderId: number
    skuId: number
    quantity: number
    price: number
    totalPrice: number
    createTime?: string
    updateTime?: string
    // 关联信息
    productName?: string
    productImage?: string
    skuName?: string
    skuImage?: string
  }
}
