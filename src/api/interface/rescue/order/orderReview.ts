import type { IPageQuery } from '@/api/interface'

export namespace IOrderReview {
  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    orderId?: number
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    originStatus?: string
    targetStatus?: string
    reviewOpinion?: string
  }

  // 新增表单
  export interface CreateForm {
    tenantId?: string
    deptId?: number
    orderId: number
    reviewUserId: number
    reviewName: string
    reviewPhone?: string
    originStatus: string
    targetStatus: string
    reviewOpinion?: string
  }

  // 编辑表单
  export interface UpdateForm {
    id: number
    tenantId?: string
    deptId?: number
    orderId: number
    reviewUserId: number
    reviewName: string
    reviewPhone?: string
    originStatus: string
    targetStatus: string
    reviewOpinion?: string
  }

  // 删除参数
  export interface DeleteParams {
    ids: number[]
  }

  // list或detail返回结构
  export interface Row {
    id: number
    tenantId?: string
    deptId?: number
    orderId: number
    reviewUserId: number
    reviewName: string
    reviewPhone?: string
    originStatus: string
    targetStatus: string
    reviewOpinion?: string
    createTime?: string
    updateTime?: string
  }
}
