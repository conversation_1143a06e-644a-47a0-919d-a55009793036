export namespace IOrder {

  // SKU数量信息
  export interface SkuQuantityBO {
    /** skuId */
    skuId: number
    /** 数量 */
    quantity: number
  }

  // 订单创建DTO
  export interface OrderCreateDTO {
    /** 任务编号 */
    workNumber?: string
    /** 类型 */
    type: string
    /** 操作原因 */
    reason: string
    /** 备注 */
    remark?: string
    /** skuId和数量列表 */
    skuQuantityList: SkuQuantityBO[]
  }

  // 订单表单数据
  export interface OrderFormData {
    /** 任务编号 */
    workNumber?: string
    /** 操作原因 */
    reason: string
    /** 备注 */
    remark?: string
  }

  // 订单详情
  export interface OrderDetail {
    /** 订单ID */
    id: number
    /** 订单编号 */
    orderNumber: string
    /** 任务编号 */
    workNumber?: string
    /** 类型 */
    type: string
    /** 操作原因 */
    reason: string
    /** 备注 */
    remark?: string
    /** 状态 */
    status: string
    /** 创建时间 */
    createTime: string
    /** 更新时间 */
    updateTime: string
    /** 订单项列表 */
    orderItems: OrderItem[]
  }

  // 订单项
  export interface OrderItem {
    /** 订单项ID */
    id: number
    /** 订单ID */
    orderId: number
    /** SKU ID */
    skuId: number
    /** 数量 */
    quantity: number
    /** 单价 */
    price: number
    /** 总价 */
    totalPrice: number
    /** 产品信息 */
    productName: string
    /** 产品图片 */
    image: string
    /** SKU名称 */
    skuName: string
    /** SKU图片 */
    skuImage: string
  }

  // 我的申请列表查询DTO
  export interface AppOrderListDTO {
    /** 页码 */
    page?: number
    /** 每页大小 */
    limit?: number
    /** 任务编号 */
    workNumber?: string
    /** 订单编号 */
    orderNumber?: string
    /** 状态 */
    status?: string
    /** 状态列表 */
    statusList?: string[]
  }

  // 订单VO
  export interface OrderVO {
    /** 记录ID */
    id: number
    /** 租户ID */
    tenantId: string
    /** 部门id */
    deptId: number
    /** 任务id */
    workId: number
    /** 任务编号 */
    workNumber: string
    /** 订单编号 */
    orderNumber: string
    /** 类型 */
    type: string
    /** 操作原因 */
    reason: string
    /** 申请用户ID */
    applyUserId: number
    /** 申请用户名 */
    applyName: string
    /** 申请用户手机号 */
    applyPhone: string
    /** 出入库 */
    operate: boolean
    /** 审批用户ID */
    reviewUserId?: number
    /** 审批人 */
    reviewName?: string
    /** 审批人电话 */
    reviewPhone?: string
    /** 审批意见 */
    reviewOpinion?: string
    /** 总金额 */
    totalPrice: number
    /** 物资总数量 */
    totalAmount: number
    /** 状态 */
    status: string
    /** 备注 */
    remark?: string
    /** 创建时间 */
    createTime: string
    /** 更新时间 */
    updateTime: string
    /** 子项 */
    orderItems?: OrderItem[]
    /** 审批记录 */
    orderReviews?: OrderReview[]
  }

  // 审批记录
  export interface OrderReview {
    /** 记录ID */
    id: number
    /** 租户ID */
    tenantId: string
    /** 部门ID */
    deptId: number
    /** 物资记录ID */
    orderId: number
    /** 审批用户ID */
    reviewUserId: number
    /** 审批人 */
    reviewName: string
    /** 审批人电话 */
    reviewPhone: string
    /** 原状态 */
    originStatus: string
    /** 审批后状态 */
    targetStatus: string
    /** 审批意见 */
    reviewOpinion: string
    /** 创建时间 */
    createTime: string
    /** 更新时间 */
    updateTime: string
  }

  // 订单审批DTO
  export interface OrderReviewDTO {
    /** 订单ID */
    id: number
    /** 审批状态 */
    status: string
    /** 审批意见 */
    reviewOpinion?: string
  }

  // 查询条件
  export interface Query {
    page?: number
    limit?: number
    tenantId?: string
    deptId?: number
    workId?: number
    workNumber?: string
    orderNumber?: string
    type?: string
    reason?: string
    applyUserId?: number
    applyName?: string
    applyPhone?: string
    operate?: boolean
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    reviewOpinion?: string
    totalPrice?: number
    totalAmount?: number
    status?: string
    createTimeStart?: string
    createTimeEnd?: string
  }

  // 我的申请查询DTO
  export interface AppOrderListDTO {
    page?: number
    limit?: number
    /** 订单类型 */
    type?: string
    /** 状态 */
    status?: string
    /** 开始时间 */
    startTime?: string
    /** 结束时间 */
    endTime?: string
  }
}
