export namespace IRescueUser {
  // 用户详情VO
  export interface UserInfoVO {
    /** 用户ID */
    id: number
    /** 系统用户ID */
    userId?: number
    /** 用户名 */
    username?: string
    /** 部门ID */
    deptId?: number
    /** 部门名称 */
    deptName?: string
    /** 真实姓名 */
    name: string
    /** 昵称 */
    nickName?: string
    /** 编号前缀 */
    numberPrefix?: string
    /** 编号 */
    number?: number
    /** 职位 */
    post?: string
    /** 头像 */
    avatar?: string
    /** 手机号 */
    phone?: string
    /** 身份证号 */
    identityCard?: string
    /** 身份证开始日期 */
    identityStartDate?: string
    /** 身份证结束日期 */
    identityEndDate?: string
    /** 护照号码 */
    passportNumber?: string
    /** 保险状态 */
    insuranceStatus?: boolean
    /** 政治面貌 */
    politicsStatus?: string
    /** 血型 */
    bloodType?: string
    /** 性别 */
    sex?: string
    /** 生日 */
    birthday?: string
    /** 备注 */
    remark?: string
    /** 签名图片 */
    signatureImage?: string
    /** 身份证图片 */
    identityImage?: string
    /** 信息图片 */
    informationImage?: string
    /** 总出勤时长 */
    totalDutyDuration?: number
    /** 年度出勤时长 */
    yearDutyDuration?: number
    /** 紧急联系人 */
    emergencyContact?: string
    /** 紧急联系人电话 */
    emergencyContactPhone?: string
    /** 病史 */
    medicalHistory?: string
    /** 过敏史 */
    allergiesHistory?: string
    /** 是否启用 */
    enable?: boolean
    /** 审批时间 */
    approveTime?: string
    /** 角色信息 */
    roles?: RoleInfo[]
  }

  // 角色信息
  export interface RoleInfo {
    /** 角色ID */
    id: number
    /** 角色名称 */
    roleName: string
    /** 备注 */
    remark?: string
    /** 删除标志 */
    delFlag?: string
    /** 创建时间 */
    createTime?: string
    /** 更新时间 */
    updateTime?: string
    /** 租户ID */
    tenantId?: string
    /** 创建人ID */
    createId?: number
    /** 更新人ID */
    updateId?: number
    /** 是否锁定 */
    isLock?: string
    /** 权限标识 */
    permissions?: string
  }

  // 更新用户信息DTO
  export interface UpdateMyDetailDTO {
    /** 真实姓名 */
    name?: string
    /** 昵称 */
    nickName?: string
    /** 手机号 */
    phone?: string
    /** 头像 */
    avatar?: string
    /** 性别 */
    sex?: string
    /** 生日 */
    birthday?: string
    /** 身份证号 */
    identityCard?: string
    /** 身份证开始日期 */
    identityStartDate?: string
    /** 身份证结束日期 */
    identityEndDate?: string
    /** 护照号码 */
    passportNumber?: string
    /** 政治面貌 */
    politicsStatus?: string
    /** 血型 */
    bloodType?: string
    /** 备注 */
    remark?: string
    /** 紧急联系人 */
    emergencyContact?: string
    /** 紧急联系人电话 */
    emergencyContactPhone?: string
    /** 病史 */
    medicalHistory?: string
    /** 过敏史 */
    allergiesHistory?: string
  }
}
