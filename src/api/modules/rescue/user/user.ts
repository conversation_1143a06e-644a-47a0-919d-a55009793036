import type { IRescueUser } from '@/api/interface/rescue/user/user'
import { http } from '@/utils/http'

/**
 * 获取当前用户详情
 * @returns 用户详情信息
 */
export function getMyDetailApi() {
  return http.get<IRescueUser.UserInfoVO>('/user-info/myDetail')
}

/**
 * 更新当前用户信息
 * @param data 更新的用户信息
 * @returns 更新结果
 */
export function updateMyDetailApi(data: IRescueUser.UpdateMyDetailDTO) {
  return http.put<boolean>('/user-info/updateMyDetail', data)
}
