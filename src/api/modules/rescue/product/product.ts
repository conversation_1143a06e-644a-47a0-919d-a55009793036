import type { IPage } from '@/api/interface/index'
import type { IProduct } from '@/api/interface/rescue/product/product'
import { RESCUE_MODULE } from '@/api/helper/prefix'
import { http } from '@/utils/http'

/**
 * 查询物资列表
 * @param params 查询参数
 * @returns 物资列表
 */
export function getProductListApi(params: IProduct.Query) {
  return http.post<IPage<IProduct.Row>>(`${RESCUE_MODULE}/product/list`, params)
}

/**
 * 物资选择器
 * @param params 查询参数
 * @returns 物资选择结果
 */
export function getProductSelectApi(params: IProduct.Query) {
  return http.post<IPage<IProduct.ProductSelectVO>>(`${RESCUE_MODULE}/product/select`, params)
}

/**
 * 添加物资
 * @param params 创建参数
 * @returns 创建结果
 */
export function createProductApi(params: IProduct.ProductCreateDTO) {
  return http.post(`${RESCUE_MODULE}/product/create`, params)
}

/**
 * 修改物资
 * @param params 更新参数
 * @returns 更新结果
 */
export function updateProductApi(params: IProduct.ProductCreateDTO) {
  return http.put(`${RESCUE_MODULE}/product/update`, params)
}

/**
 * 删除物资
 * @param params 删除参数
 * @returns 删除结果
 */
export function removeProductApi(params: { ids: (string | number)[] }) {
  return http.delete(`${RESCUE_MODULE}/product/remove`, params)
}

/**
 * 获取物资详情
 * @param params 详情参数
 * @returns 物资详情
 */
export function getProductDetailApi(params: { id: number }) {
  return http.get<IProduct.Row>(`${RESCUE_MODULE}/product/detail`, params)
}

/**
 * 获取产品规格详情
 * @param id 产品规格ID
 * @returns 产品规格详情
 */
export function getProductSkuDetailApi(id: number) {
  return http.get<IProduct.ProductSkuDetail>(`${RESCUE_MODULE}/product/sku/${id}`)
}

// TODO: 后续实现文件上传和下载功能
/**
 * 导入excel
 * @param file 文件
 * @returns 导入结果
 */
// export function importProductExcelApi(file: any) {
//   return http.upload(RESCUE_MODULE + `/product/import`, file)
// }

/**
 * 导出excel
 * @param params 导出参数
 * @returns 导出结果
 */
// export function exportProductExcelApi(params: IProduct.Query) {
//   return http.download(RESCUE_MODULE + `/product/export`, params)
// }
