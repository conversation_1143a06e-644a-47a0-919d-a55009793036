import type { IPage } from '@/api/interface'
import type { IOrder } from '@/api/interface/rescue/order/order'
import { RESCUE_MODULE } from '@/api/helper/prefix'
import { http } from '@/utils/http'

/**
 * 创建订单
 * @param data 订单创建数据
 * @returns 创建结果
 */
export function createOrderApi(data: IOrder.OrderCreateDTO) {
  return http.post<void>('/order/create', data)
}

/**
 * 查询订单列表
 * @param params 查询参数
 * @returns 订单列表
 */
export function getOrderListApi(params: IOrder.Query) {
  return http.get<IPage<IOrder.OrderVO>>(`${RESCUE_MODULE}/order/list`, params)
}

/**
 * 获取我的申请列表
 * @param params 查询参数
 * @returns 我的申请列表
 */
export function getMyApplyListApi(params: IOrder.AppOrderListDTO) {
  return http.post<IPage<IOrder.OrderVO>>('/order/my/apply/list', params)
}

/**
 * 获取订单详情
 * @param id 订单ID
 * @returns 订单详情
 */
export function getOrderDetailApi(id: number) {
  return http.get<IOrder.OrderVO>('/order/detail', { id })
}

/**
 * 审批订单
 * @param params 审批参数
 * @returns 审批结果
 */
export function reviewOrderApi(params: IOrder.OrderReviewDTO) {
  return http.put(`${RESCUE_MODULE}/order/review`, params)
}

/**
 * 删除订单
 * @param params 删除参数
 * @returns 删除结果
 */
export function removeOrderApi(params: { ids: number[] }) {
  return http.delete(`${RESCUE_MODULE}/order`, params)
}

/**
 * 获取待审批订单列表
 * @param params 查询参数
 * @returns 待审批订单列表
 */
export function getPendingOrderListApi(params: IOrder.Query) {
  return http.get<IPage<IOrder.OrderVO>>(`${RESCUE_MODULE}/order/pending`, params)
}

/**
 * 撤销订单
 * @param params 订单ID
 * @returns 撤销结果
 */
export function cancelOrderApi(params: { id: number }) {
  const { id } = params
  return http.put(`${RESCUE_MODULE}/order/${id}/cancel`)
}

/**
 * 完成订单
 * @param params 订单ID
 * @returns 完成结果
 */
export function completeOrderApi(params: { id: number }) {
  const { id } = params
  return http.put(`${RESCUE_MODULE}/order/${id}/complete`)
}

/**
 * 获取订单统计信息
 * @returns 统计信息
 */
export function getOrderStatsApi() {
  return http.get<{
    totalCount: number
    pendingCount: number
    approvedCount: number
    rejectedCount: number
    completedCount: number
  }>(`${RESCUE_MODULE}/order/stats`)
}
