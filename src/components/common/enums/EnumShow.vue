<script setup lang="ts">
import type { BaseEnum } from '@/enums/base'

interface Props {
  // 枚举对象
  enum: { [key: string]: BaseEnum }
  // 要显示的代码
  code: string | number
  // 预设主题
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'secondary'
  // 显示样式
  variant?: 'filled' | 'outlined' | 'ghost' | 'minimal'
  // 尺寸
  size?: 'small' | 'medium' | 'large'
  // 形状
  shape?: 'rounded' | 'pill' | 'square'
  // 是否可点击
  clickable?: boolean
  // 是否显示图标
  showIcon?: boolean
  // 自定义样式（保持向后兼容）
  backgroundColor?: string
  color?: string
  borderColor?: string
  fontSize?: string
  fontWeight?: string
  padding?: string
  borderRadius?: string
  boxShadow?: string
  textShadow?: string
  minWidth?: string
  textAlign?: string
  transform?: string
  border?: string
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'default',
  variant: 'filled',
  size: 'medium',
  shape: 'rounded',
  clickable: false,
  showIcon: false,
  backgroundColor: '',
  color: '',
  borderColor: '',
  fontSize: '',
  fontWeight: '',
  padding: '',
  borderRadius: '',
  boxShadow: '',
  textShadow: '',
  minWidth: '',
  textAlign: '',
  transform: '',
  border: '',
})

const emit = defineEmits(['click'])

// 计算显示值
const displayValue = computed(() => {
  const enumValue = Object.values(props.enum).find((item) => item.code === props.code)
  return enumValue?.name || props.code
})

// 计算枚举对象
const enumObject = computed(() => {
  return Object.values(props.enum).find((item) => item.code === props.code)
})

// 计算图标
const iconName = computed(() => {
  if (!props.showIcon) return ''

  // 根据主题返回对应图标
  const iconMap = {
    success: 'check-circle',
    warning: 'alert-triangle',
    danger: 'x-circle',
    info: 'info-circle',
    primary: 'star',
    secondary: 'tag',
    default: 'tag',
  }

  return enumObject.value?.icon || iconMap[props.theme]
})

// 计算CSS类名
const computedClass = computed(() => {
  const classes = [
    'enum-show',
    `enum-show--${props.theme}`,
    `enum-show--${props.variant}`,
    `enum-show--${props.size}`,
    `enum-show--${props.shape}`,
  ]

  if (props.clickable) {
    classes.push('enum-show--clickable')
  }

  return classes
})

// 计算自定义样式（保持向后兼容）
const customStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.backgroundColor) {
    style.backgroundColor = props.backgroundColor
  }

  if (props.color) {
    style.color = props.color
  }

  if (props.borderColor) {
    style.borderColor = props.borderColor
    style.borderWidth = '2rpx'
    style.borderStyle = 'solid'
  }

  if (props.fontSize) {
    style.fontSize = props.fontSize
  }

  if (props.fontWeight) {
    style.fontWeight = props.fontWeight
  }

  if (props.padding) {
    style.padding = props.padding
  }

  if (props.borderRadius) {
    style.borderRadius = props.borderRadius
  }

  if (props.boxShadow) {
    style.boxShadow = props.boxShadow
  }

  if (props.textShadow) {
    style.textShadow = props.textShadow
  }

  if (props.minWidth) {
    style.minWidth = props.minWidth
  }

  if (props.textAlign) {
    style.textAlign = props.textAlign
  }

  if (props.transform) {
    style.transform = props.transform
  }

  if (props.border) {
    style.border = props.border
  }

  return style
})

// 处理点击事件
function handleClick(event: Event) {
  if (props.clickable) {
    emit('click', event, enumObject.value)
  }
}
</script>

<template>
  <view :class="computedClass" :style="customStyle" :data-code="code" @click="handleClick">
    <!-- 图标 -->
    <wd-icon v-if="showIcon && iconName" :name="iconName" class="enum-show__icon" />

    <!-- 文本内容 -->
    <view class="enum-show__text">
      {{ displayValue }}
    </view>

    <!-- 工具提示 -->
    <view v-if="enumObject?.tooltip" class="enum-show__tooltip">
      {{ enumObject.tooltip }}
    </view>
  </view>
</template>

<style scoped lang="scss">
// Apple 设计系统变量
:root {
  --apple-blue: #007aff;
  --apple-blue-light: #5ac8fa;
  --apple-blue-dark: #0056cc;
  --apple-green: #34c759;
  --apple-green-light: #30d158;
  --apple-green-dark: #248a3d;
  --apple-orange: #ff9500;
  --apple-orange-light: #ff9f0a;
  --apple-orange-dark: #c7750a;
  --apple-red: #ff3b30;
  --apple-red-light: #ff453a;
  --apple-red-dark: #d70015;
  --apple-purple: #af52de;
  --apple-purple-light: #bf5af2;
  --apple-purple-dark: #8e44ad;
  --apple-gray: #8e8e93;
  --apple-gray-light: #c7c7cc;
  --apple-gray-dark: #48484a;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-text: #1d1d1f;
  --apple-text-secondary: #86868b;
  --apple-radius: 24rpx;
  --apple-radius-small: 16rpx;
  --apple-radius-large: 32rpx;
  --apple-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  --apple-shadow-elevated: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.enum-show {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 500;
  line-height: 1.2;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  user-select: none;

  &__icon {
    flex-shrink: 0;
  }

  &__text {
    flex: 1;
    min-width: 0;
  }

  &__tooltip {
    font-size: 0.85em;
    opacity: 0.7;
    margin-left: 8rpx;
  }

  // 尺寸变化
  &--small {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    min-height: 48rpx;
    gap: 4rpx;

    .enum-show__icon {
      font-size: 24rpx;
    }
  }

  &--medium {
    padding: 12rpx 24rpx;
    font-size: 28rpx;
    min-height: 64rpx;
    gap: 8rpx;

    .enum-show__icon {
      font-size: 28rpx;
    }
  }

  &--large {
    padding: 16rpx 32rpx;
    font-size: 32rpx;
    min-height: 80rpx;
    gap: 12rpx;

    .enum-show__icon {
      font-size: 32rpx;
    }
  }

  // 形状变化
  &--rounded {
    border-radius: var(--apple-radius-small);
  }

  &--pill {
    border-radius: 100rpx;
  }

  &--square {
    border-radius: 8rpx;
  }

  // 可点击状态
  &--clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: var(--apple-shadow);
    }

    &:active {
      transform: translateY(0) scale(0.98);
    }
  }

  // 主题变化
  &--default {
    &.enum-show--filled {
      background-color: var(--apple-background);
      color: var(--apple-text);
      border: 2rpx solid var(--apple-gray-light);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-text);
      border: 2rpx solid var(--apple-gray-light);
    }

    &.enum-show--ghost {
      background-color: transparent;
      color: var(--apple-text);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-text-secondary);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--primary {
    &.enum-show--filled {
      background-color: var(--apple-blue);
      color: white;
      border: 2rpx solid var(--apple-blue);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-blue);
      border: 2rpx solid var(--apple-blue);
    }

    &.enum-show--ghost {
      background-color: rgba(0, 122, 255, 0.1);
      color: var(--apple-blue);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-blue);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--success {
    &.enum-show--filled {
      background-color: var(--apple-green);
      color: white;
      border: 2rpx solid var(--apple-green);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-green);
      border: 2rpx solid var(--apple-green);
    }

    &.enum-show--ghost {
      background-color: rgba(52, 199, 89, 0.1);
      color: var(--apple-green);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-green);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--warning {
    &.enum-show--filled {
      background-color: var(--apple-orange);
      color: white;
      border: 2rpx solid var(--apple-orange);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-orange);
      border: 2rpx solid var(--apple-orange);
    }

    &.enum-show--ghost {
      background-color: rgba(255, 149, 0, 0.1);
      color: var(--apple-orange);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-orange);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--danger {
    &.enum-show--filled {
      background-color: var(--apple-red);
      color: white;
      border: 2rpx solid var(--apple-red);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-red);
      border: 2rpx solid var(--apple-red);
    }

    &.enum-show--ghost {
      background-color: rgba(255, 59, 48, 0.1);
      color: var(--apple-red);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-red);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--info {
    &.enum-show--filled {
      background-color: var(--apple-blue-light);
      color: white;
      border: 2rpx solid var(--apple-blue-light);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-blue-light);
      border: 2rpx solid var(--apple-blue-light);
    }

    &.enum-show--ghost {
      background-color: rgba(90, 200, 250, 0.1);
      color: var(--apple-blue-light);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-blue-light);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  &--secondary {
    &.enum-show--filled {
      background-color: var(--apple-gray);
      color: white;
      border: 2rpx solid var(--apple-gray);
    }

    &.enum-show--outlined {
      background-color: transparent;
      color: var(--apple-gray);
      border: 2rpx solid var(--apple-gray);
    }

    &.enum-show--ghost {
      background-color: rgba(142, 142, 147, 0.1);
      color: var(--apple-gray);
      border: none;
    }

    &.enum-show--minimal {
      background-color: transparent;
      color: var(--apple-gray);
      border: none;
      padding: 4rpx 8rpx;
    }
  }

  // 悬停和焦点状态增强
  &--clickable {
    &:hover {
      &.enum-show--filled {
        filter: brightness(1.1);
      }

      &.enum-show--outlined {
        background-color: rgba(0, 0, 0, 0.04);
      }

      &.enum-show--ghost {
        filter: brightness(1.1);
      }
    }

    &:active {
      &.enum-show--filled {
        filter: brightness(0.9);
      }

      &.enum-show--outlined {
        background-color: rgba(0, 0, 0, 0.08);
      }

      &.enum-show--ghost {
        filter: brightness(0.9);
      }
    }
  }
}
</style>
