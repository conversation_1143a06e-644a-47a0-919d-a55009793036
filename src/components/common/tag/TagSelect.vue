<script setup lang="ts">
import type { IMetaTag } from '@/api/interface/system/meta/metaTag'
import type { IMetaTagCategory } from '@/api/interface/system/meta/metaTagCategory'
import {
  createMetaTagApi,
  getMetaTagCategoryCountApi,
  getMetaTagListApi,
} from '@/api/modules/system/meta/metaTag'
import { getMetaTagCategoryListApi } from '@/api/modules/system/meta/metaTagCategory'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const modelValue = defineModel<number | number[]>()

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  placeholder: '选择标签',
  showCreateButton: true,
  showCategoryFilter: true,
  frequentTagIds: () => [],
  maxTagCount: 10,
})

const emit = defineEmits(['change', 'create'])

interface Props {
  multiple?: boolean
  placeholder?: string
  showCreateButton?: boolean
  showCategoryFilter?: boolean
  frequentTagIds?: number[]
  maxTagCount?: number
}

// ============ 状态管理 ============
const loading = ref(false)
const submitting = ref(false)
const showTagPicker = ref(false)
const showCreateDialog = ref(false)
const showCategoryPicker = ref(false)

// ============ 数据状态 ============
const tagOptions = ref<IMetaTag.Row[]>([])
const frequentTags = ref<IMetaTag.Row[]>([])
const categories = ref<IMetaTagCategory.Row[]>([])
const categoryCount = ref<Record<number, number>>({})
const selectedCategoryId = ref<number | undefined>(undefined)
const searchQuery = ref('')

// ============ 分页相关 ============
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMoreData = ref(false)

// ============ 表单相关 ============
const tagFormRef = ref()
const newTag = ref<IMetaTag.Form>({
  name: '',
  backgroundColor: '#007AFF',
  textColor: '#FFFFFF',
  plain: 'false',
  enable: 'true',
  categoryId: undefined,
})

const rules = {
  name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }],
  backgroundColor: [{ required: true, message: '请选择背景颜色', trigger: 'change' }],
  textColor: [{ required: true, message: '请选择文字颜色', trigger: 'change' }],
}

// Apple风格颜色预设
const appleColors = [
  '#007AFF', // iOS蓝色
  '#34C759', // 系统绿色
  '#FF3B30', // 系统红色
  '#FF9500', // 系统橙色
  '#FFCC00', // 系统黄色
  '#5856D6', // 系统紫色
  '#FF2D92', // 系统粉色
  '#8E8E93', // 系统灰色
  '#000000', // 黑色
  '#FFFFFF', // 白色
]

// ============ 计算属性 ============
const selectedTagsData = computed(() => {
  if (!modelValue.value || (Array.isArray(modelValue.value) && modelValue.value.length === 0)) {
    return []
  }

  const ids = Array.isArray(modelValue.value) ? modelValue.value : [modelValue.value]
  const allTags = [...tagOptions.value, ...frequentTags.value]

  return ids
    .map((id) => {
      const tag = allTags.find((t) => t.id === id)
      return (
        tag || {
          id,
          name: `标签${id}`,
          backgroundColor: '#007AFF',
          textColor: '#FFFFFF',
          plain: false,
        }
      )
    })
    .filter(Boolean)
})

const selectedCategoryName = computed(() => {
  if (!selectedCategoryId.value) return '全部分类'
  const category = categories.value.find((c) => c.id === selectedCategoryId.value)
  return category?.name || '全部分类'
})

const tagSelectorText = computed(() => {
  const count = selectedTagsData.value.length
  if (count === 0) return props.placeholder
  if (props.multiple) {
    return count === 1 ? selectedTagsData.value[0].name : `已选 ${count} 个标签`
  }
  return selectedTagsData.value[0]?.name || props.placeholder
})

const categoryColumns = computed(() => [
  [
    { label: '全部分类', value: undefined },
    ...categories.value.map((category) => ({
      label: `${category.name} (${categoryCount.value[category.id as number] || 0})`,
      value: category.id,
    })),
  ],
])

// ============ 样式工具函数 ============
function getTagStyle(tag: any) {
  const baseStyle = {
    borderRadius: '24rpx',
    fontWeight: '400',
    fontSize: '24rpx',
    border: '2rpx solid',
  }

  // 确保使用接口返回的颜色字段
  const bgColor = tag.backgroundColor || '#007AFF'
  const textColor = tag.textColor || '#FFFFFF'

  if (tag.plain === 'true' || tag.plain === true) {
    return {
      ...baseStyle,
      color: bgColor,
      borderColor: bgColor,
      backgroundColor: 'transparent',
    }
  } else {
    return {
      ...baseStyle,
      color: textColor,
      backgroundColor: bgColor,
      borderColor: bgColor,
    }
  }
}

// ============ 数据加载函数 ============
async function loadCategories() {
  try {
    const res = await getMetaTagCategoryListApi({
      enable: 'true',
      page: 1,
      limit: 100,
    })
    categories.value = res.data.rows || []
  } catch (error) {
    console.error('获取标签分类失败:', error)
  }
}

async function loadCategoryCount() {
  try {
    const res = await getMetaTagCategoryCountApi()
    if (res.data?.length) {
      const countMap: Record<number, number> = {}
      res.data.forEach((item) => {
        countMap[item.categoryId] = item.count
      })
      categoryCount.value = countMap
    }
  } catch (error) {
    console.error('获取分类标签数量失败:', error)
  }
}

async function loadTagData() {
  loading.value = true
  try {
    // 构建请求参数，过滤掉undefined值
    const params: any = {
      enable: 'true',
      page: currentPage.value,
      limit: pageSize.value,
    }

    // 只有当有搜索关键词时才添加name参数
    if (searchQuery.value && searchQuery.value.trim()) {
      params.name = searchQuery.value.trim()
    }

    // 只有当选择了分类时才添加categoryId参数
    if (selectedCategoryId.value !== undefined && selectedCategoryId.value !== null) {
      params.categoryId = selectedCategoryId.value
    }

    const res = await getMetaTagListApi(params)

    if (currentPage.value === 1) {
      tagOptions.value = res.data.rows || []
    } else {
      tagOptions.value = [...tagOptions.value, ...(res.data.rows || [])]
    }

    total.value = res.data.total || 0
    hasMoreData.value = tagOptions.value.length < total.value
  } catch (error) {
    console.error('获取标签列表失败:', error)
  } finally {
    loading.value = false
  }
}

async function loadFrequentTags() {
  if (!props.frequentTagIds?.length) return

  try {
    const res = await getMetaTagListApi({
      page: 1,
      limit: 100,
      enable: 'true',
    })

    if (res.data.rows?.length) {
      frequentTags.value = res.data.rows.filter((tag) =>
        props.frequentTagIds.includes(Number(tag.id)),
      )
    }
  } catch (error) {
    console.error('获取常用标签失败:', error)
  }
}

async function loadMoreData() {
  if (loading.value || !hasMoreData.value) return
  currentPage.value++
  await loadTagData()
}

// ============ 事件处理函数 ============
// function handleCategoryChange(value: any) {
//   selectedCategoryId.value = value[0]
//   currentPage.value = 1
//   loadTagData()
//   showCategoryPicker.value = false
// }

function handleCategorySelect(categoryId: number | undefined) {
  selectedCategoryId.value = categoryId
  currentPage.value = 1
  loadTagData()
  showCategoryPicker.value = false
}

function handleSearch() {
  currentPage.value = 1
  loadTagData()
}

// function handleSearchInput() {
//   // 移除实时搜索，只在用户点击搜索按钮时才搜索
// }

function toggleTag(tagId: number) {
  if (props.multiple) {
    const ids = Array.isArray(modelValue.value) ? modelValue.value : []
    const index = ids.indexOf(tagId)

    if (index > -1) {
      ids.splice(index, 1)
    } else {
      if (ids.length < props.maxTagCount) {
        ids.push(tagId)
      } else {
        uni.showToast({
          title: `最多选择${props.maxTagCount}个标签`,
          icon: 'none',
        })
        return
      }
    }
    modelValue.value = ids
  } else {
    modelValue.value = tagId
    showTagPicker.value = false
  }
}

function removeTag(tagId: number) {
  if (props.multiple) {
    const ids = Array.isArray(modelValue.value) ? modelValue.value : []
    const index = ids.indexOf(tagId)
    if (index > -1) {
      ids.splice(index, 1)
      modelValue.value = ids
    }
  } else {
    modelValue.value = 0
  }
}

function isTagSelected(tagId: number) {
  if (props.multiple) {
    return Array.isArray(modelValue.value) && modelValue.value.includes(tagId)
  } else {
    return modelValue.value === tagId
  }
}

async function submitNewTag() {
  if (!tagFormRef.value) return

  try {
    await tagFormRef.value.validate()
    submitting.value = true

    const tagData = {
      ...newTag.value,
      plain: newTag.value.plain ? 'true' : 'false',
    }

    await createMetaTagApi(tagData)
    uni.showToast({
      title: '标签创建成功',
      icon: 'success',
    })

    showCreateDialog.value = false
    loadTagData()
    loadCategoryCount()

    // 重置表单
    newTag.value = {
      name: '',
      backgroundColor: '#007AFF',
      textColor: '#FFFFFF',
      plain: 'false',
      enable: 'true',
      categoryId: undefined,
    }

    emit('create')
  } catch (error) {
    console.error('创建标签失败:', error)
    uni.showToast({
      title: '创建标签失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// ============ 初始化 ============
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([loadCategories(), loadCategoryCount(), loadTagData(), loadFrequentTags()])
  } catch (error) {
    console.error('初始化标签选择器失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <view class="tag-select-container">
    <!-- 主选择器 - 一行布局 -->
    <view class="main-selector">
      <!-- 分类选择按钮 -->
      <view v-if="showCategoryFilter" class="category-button" @click="showCategoryPicker = true">
        <view class="category-text">
          {{ selectedCategoryName }}
        </view>
        <view class="chevron">›</view>
      </view>

      <!-- 标签选择按钮 -->
      <view class="tag-button" @click="showTagPicker = true">
        <view class="tag-content">
          <view class="tag-text" :class="{ placeholder: selectedTagsData.length === 0 }">
            {{ tagSelectorText }}
          </view>
          <view class="chevron">›</view>
        </view>
      </view>
    </view>

    <!-- 已选标签显示 -->
    <view v-if="selectedTagsData.length > 0 && props.multiple" class="selected-tags-display">
      <view
        v-for="tag in selectedTagsData"
        :key="tag.id"
        class="selected-tag-chip"
        :style="getTagStyle(tag)"
        @click="removeTag(tag.id)"
      >
        <view class="chip-text">
          {{ tag.name }}
        </view>
        <view class="chip-remove">×</view>
      </view>
    </view>

    <!-- 分类选择弹窗 -->
    <wd-popup
      v-model="showCategoryPicker"
      position="bottom"
      :close-on-click-modal="true"
      custom-class="category-popup"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="99999"
    >
      <view class="popup-header">
        <view class="popup-title">选择分类</view>
        <wd-button type="text" size="small" @click="showCategoryPicker = false">完成</wd-button>
      </view>
      <view class="category-list">
        <view
          v-for="category in [{ id: undefined, name: '全部分类' }, ...categories]"
          :key="category.id || 'all'"
          class="category-item"
          :class="{ active: selectedCategoryId === category.id }"
          @click="handleCategorySelect(category.id)"
        >
          <view class="category-name">
            {{ category.name }}
          </view>
          <view v-if="category.id && categoryCount[category.id]" class="category-count">
            ({{ categoryCount[category.id] }})
          </view>
          <view v-if="selectedCategoryId === category.id" class="check-mark">✓</view>
        </view>
      </view>
    </wd-popup>

    <!-- 标签选择弹窗 -->
    <wd-popup
      v-model="showTagPicker"
      position="bottom"
      :close-on-click-modal="true"
      custom-class="tag-popup"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="999"
    >
      <view class="popup-header">
        <view class="popup-title">
          {{ props.multiple ? '选择标签' : '选择标签' }}
        </view>
        <view class="header-actions">
          <wd-button
            v-if="showCreateButton"
            type="text"
            size="small"
            @click="showCreateDialog = true"
          >
            新建
          </wd-button>
          <wd-button type="primary" size="small" @click="showTagPicker = false">完成</wd-button>
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="search-container">
        <view class="search-wrapper">
          <wd-input
            v-model="searchQuery"
            placeholder="搜索标签"
            clearable
            no-border
            custom-class="search-input"
            custom-style="flex: 1"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <wd-icon name="search" size="32rpx" color="#8E8E93" />
            </template>
          </wd-input>
          <wd-button type="primary" size="small" custom-class="search-button" @click="handleSearch">
            搜索
          </wd-button>
        </view>
      </view>

      <!-- 标签列表 -->
      <scroll-view class="tag-scroll-view" scroll-y @scrolltolower="loadMoreData">
        <!-- 常用标签 -->
        <view v-if="frequentTags.length > 0" class="tag-section">
          <view class="section-title">常用标签</view>
          <view class="tag-grid">
            <view
              v-for="tag in frequentTags"
              :key="`frequent-${tag.id}`"
              class="tag-item"
              :class="{ selected: isTagSelected(tag.id) }"
              :style="getTagStyle(tag)"
              @click="toggleTag(tag.id)"
            >
              <view class="tag-label">
                {{ tag.name }}
              </view>
              <view v-if="isTagSelected(tag.id)" class="check-mark">✓</view>
            </view>
          </view>
        </view>

        <!-- 所有标签 -->
        <view v-if="tagOptions.length > 0" class="tag-section">
          <view class="section-title">
            {{ frequentTags.length ? '全部标签' : '标签列表' }}
          </view>
          <view class="tag-grid">
            <view
              v-for="tag in tagOptions"
              :key="`all-${tag.id}`"
              class="tag-item"
              :class="{ selected: isTagSelected(tag.id) }"
              :style="getTagStyle(tag)"
              @click="toggleTag(tag.id)"
            >
              <view class="tag-label">
                {{ tag.name }}
              </view>
              <view v-if="isTagSelected(tag.id)" class="check-mark">✓</view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="hasMoreData" class="load-more-container">
          <view v-if="!loading" class="load-more-btn" @click="loadMoreData">
            <view class="load-more-text">加载更多</view>
            <view class="load-more-arrow">↓</view>
          </view>
          <view v-else class="loading-indicator">
            <wd-loading size="32rpx" />
            <view>加载中...</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view
          v-if="!loading && tagOptions.length === 0 && frequentTags.length === 0"
          class="empty-state"
        >
          <view class="empty-text">暂无标签</view>
        </view>
      </scroll-view>
    </wd-popup>

    <!-- 创建标签弹窗 -->
    <wd-popup
      v-model="showCreateDialog"
      position="center"
      :close-on-click-modal="false"
      custom-class="create-popup"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="9999"
    >
      <view class="create-header">
        <view class="create-title">新建标签</view>
        <wd-icon name="close" size="40rpx" @click="showCreateDialog = false" />
      </view>

      <view class="create-content">
        <wd-form ref="tagFormRef" :model="newTag" :rules="rules">
          <!-- 基础信息 -->
          <view class="input-group">
            <wd-cell title-width="0" custom-class="p-0!">
              <wd-input v-model="newTag.name" placeholder="标签名称" clearable no-border />
            </wd-cell>
          </view>

          <view class="input-group">
            <wd-picker
              v-model="newTag.categoryId"
              :columns="categoryColumns"
              placeholder="选择分类"
            >
              <wd-input :value="selectedCategoryName || '选择分类'" readonly no-border />
            </wd-picker>
          </view>

          <!-- 颜色选择 -->
          <view class="color-section">
            <view class="color-row">
              <view class="color-label">背景</view>
              <view class="color-options">
                <view
                  v-for="color in appleColors.slice(0, 6)"
                  :key="`bg-${color}`"
                  class="color-dot"
                  :class="{ active: newTag.backgroundColor === color }"
                  :style="{ backgroundColor: color }"
                  @click="newTag.backgroundColor = color"
                />
              </view>
            </view>

            <view class="color-row">
              <view class="color-label">文字</view>
              <view class="color-options">
                <view
                  v-for="color in appleColors.slice(0, 6)"
                  :key="`text-${color}`"
                  class="color-dot"
                  :class="{ active: newTag.textColor === color }"
                  :style="{ backgroundColor: color }"
                  @click="newTag.textColor = color"
                />
              </view>
            </view>

            <view class="style-toggle">
              <view class="toggle-label">镂空样式</view>
              <wd-switch v-model="newTag.plain" size="small" />
            </view>
          </view>

          <!-- 预览 -->
          <view class="preview-section">
            <view class="preview-tag" :style="getTagStyle(newTag)">
              <view>{{ newTag.name || '预览效果' }}</view>
            </view>
          </view>
        </wd-form>
      </view>

      <view class="create-footer">
        <wd-button size="large" custom-class="flex-1" type="info" @click="showCreateDialog = false">
          取消
        </wd-button>
        <wd-button
          size="large"
          custom-class="flex-1"
          type="primary"
          :loading="submitting"
          @click="submitNewTag"
        >
          创建
        </wd-button>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
.tag-select-container {
  width: 100%;
}

// ============ 主选择器样式 ============
.main-selector {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 16rpx 0;

  .category-button,
  .tag-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx;
    background: #f2f2f7;
    border-radius: 24rpx;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    &:active {
      background: #e5e5ea;
      transform: scale(0.97);
      box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
    }
  }

  .category-button {
    min-width: 110px;

    .category-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #1d1d1f;
      margin-right: 12rpx;
      letter-spacing: -0.2rpx;
    }
  }

  .tag-button {
    flex: 1;

    .tag-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .tag-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #1d1d1f;
      letter-spacing: -0.2rpx;

      &.placeholder {
        color: #8e8e93;
        font-weight: 400;
      }
    }
  }

  .chevron {
    font-size: 28rpx;
    color: #8e8e93;
    font-weight: 400;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }
}

// ============ 已选标签显示 ============
.selected-tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;

  .selected-tag-chip {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    border: 2rpx solid;
    transition: all 0.2s ease;
    max-height: 48rpx;

    &:active {
      transform: scale(0.95);
    }

    .chip-text {
      margin-right: 8rpx;
      font-size: 22rpx;
      font-weight: 400;
    }

    .chip-remove {
      font-size: 24rpx;
      font-weight: bold;
      line-height: 1;
    }
  }
}

// ============ 弹窗通用样式 ============
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 48rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
  background: rgba(248, 248, 248, 0.95);
  backdrop-filter: blur(40rpx);

  .popup-title {
    font-size: 34rpx;
    font-weight: 600;
    color: #1d1d1f;
    letter-spacing: -0.86rpx;
  }

  .header-actions {
    display: flex;
    gap: 32rpx;
  }
}

// ============ 分类弹窗样式 ============
:deep(.category-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .popup-header {
    background: #ffffff;
  }

  .category-list {
    background: #ffffff;
    max-height: 50vh;
    min-height: 30vh;
    overflow-y: auto;

    .category-item {
      display: flex;
      align-items: center;
      gap: 20rpx;
      padding: 32rpx 40rpx;
      border-bottom: 2rpx solid #f2f2f7;
      transition: all 0.2s ease;

      &:active {
        background: #f2f2f7;
      }

      &.active {
        background: rgba(0, 122, 255, 0.1);
      }

      &:last-child {
        border-bottom: none;
      }

      .category-name {
        font-size: 32rpx;
        color: #000000;
        font-weight: 500;
      }

      .category-count {
        font-size: 28rpx;
        color: #8e8e93;
        margin-left: 16rpx;
      }

      .check-mark {
        font-size: 32rpx;
        color: #007aff;
        font-weight: bold;
        margin-left: auto;
      }
    }
  }
}

// ============ 标签弹窗样式 ============
:deep(.tag-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .search-container {
    padding: 32rpx 48rpx;
    background: #ffffff;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

    .search-wrapper {
      display: flex;
      align-items: center;
      gap: 24rpx;

      :deep(.search-input) {
        flex: 1;
        background: #f2f2f7;
        border-radius: 24rpx;
        border: none;
        font-size: 32rpx;

        &:focus {
          background: #e5e5ea;
          box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.2);
        }
      }

      :deep(.search-button) {
        min-width: 120rpx;
        border-radius: 40rpx;
        padding: 16rpx 32rpx;
        font-weight: 600;
        font-size: 30rpx;
        letter-spacing: -0.48rpx;
      }
    }
  }

  .tag-scroll-view {
    height: 50vh;
    background: #ffffff;

    .tag-section {
      padding: 40rpx;
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

      &:last-child {
        border-bottom: none;
        padding-bottom: 56rpx;
      }

      .section-title {
        font-size: 24rpx;
        font-weight: 600;
        color: #8e8e93;
        margin-bottom: 24rpx;
        text-transform: uppercase;
        letter-spacing: 1.4rpx;
      }

      .tag-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .tag-item {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16rpx 28rpx;
          border-radius: 32rpx;
          font-size: 28rpx;
          border: 2rpx solid;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          min-height: 64rpx;
          font-weight: 400;
          letter-spacing: -0.2rpx;

          &:active {
            transform: scale(0.95);
          }

          &.selected {
            box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.15);
            transform: scale(1.01);
          }

          .tag-label {
            flex: 1;
            text-align: center;
            white-space: nowrap;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 1.2;
          }

          .check-mark {
            position: absolute;
            top: -8rpx;
            right: -8rpx;
            width: 32rpx;
            height: 32rpx;
            background: #007aff;
            color: #ffffff;
            border-radius: 16rpx;
            font-size: 20rpx;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3rpx solid #ffffff;
            box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
          }
        }
      }
    }

    .load-more-container {
      display: flex;
      justify-content: center;
      padding: 40rpx;

      .load-more-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 16rpx;
        padding: 24rpx 48rpx;
        background: #f2f2f7;
        border-radius: 40rpx;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &:active {
          background: #e5e5ea;
          transform: scale(0.95);
        }

        .load-more-text {
          font-size: 30rpx;
          color: #007aff;
          font-weight: 500;
          letter-spacing: -0.48rpx;
        }

        .load-more-arrow {
          font-size: 28rpx;
          color: #007aff;
          animation: bounce 2s infinite;
        }
      }

      .loading-indicator {
        display: flex;
        align-items: center;
        gap: 16rpx;
        color: #8e8e93;
        font-size: 30rpx;
      }
    }

    .empty-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 120rpx 40rpx;

      .empty-text {
        color: #8e8e93;
        font-size: 32rpx;
      }
    }
  }
}

// ============ 通用动画 ============
.tag-select-container {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}

// ============ 创建弹窗样式 ============
:deep(.create-popup) {
  border-radius: 32rpx;
  .create-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 40rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    background: rgba(248, 248, 248, 0.95);
    backdrop-filter: blur(40rpx);

    .create-title {
      font-size: 34rpx;
      font-weight: 600;
      color: #1d1d1f;
      letter-spacing: -0.86rpx;
    }
  }

  .create-content {
    padding: 32rpx;
    max-height: 80vh;
    background: #ffffff;

    .input-group {
      margin-bottom: 24rpx;
    }

    .color-section {
      margin: 32rpx 0;
      padding: 32rpx;
      background: #f8f9fa;
      border-radius: 24rpx;

      .color-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .color-label {
          font-size: 26rpx;
          color: #1d1d1f;
          font-weight: 500;
          min-width: 64rpx;
        }

        .color-options {
          display: flex;
          gap: 16rpx;

          .color-dot {
            width: 48rpx;
            height: 48rpx;
            border-radius: 24rpx;
            border: 4rpx solid transparent;
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;

            &.active {
              border-color: #007aff;
              transform: scale(1.05);
              box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
            }

            &:active {
              transform: scale(0.92);
            }
          }
        }
      }

      .style-toggle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 24rpx;
        border-top: 2rpx solid #e5e5ea;
        margin-top: 24rpx;

        .toggle-label {
          font-size: 26rpx;
          color: #1d1d1f;
          font-weight: 500;
        }
      }
    }

    .preview-section {
      display: flex;
      justify-content: center;
      padding: 32rpx 0;

      .preview-tag {
        padding: 12rpx 24rpx;
        border-radius: 24rpx;
        font-size: 24rpx;
        font-weight: 400;
        border: 2rpx solid;
        transition: all 0.2s ease;
        max-height: 56rpx;
        display: flex;
        align-items: center;
      }
    }
  }

  .create-footer {
    display: flex;
    justify-content: space-between;
    gap: 40rpx;
    padding: 32rpx 40rpx;
    border-top: 2rpx solid #e5e5ea;
    background: #fafafa;
  }
}
</style>
