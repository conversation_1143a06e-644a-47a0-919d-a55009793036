<script setup lang="ts">
import type { ISysDept } from '@/api/interface/system/admin/dept'

defineOptions({
  name: 'DeptTreeNode',
})

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [node: ISysDept.Tree, selected: boolean]
  toggle: [nodeId: number, expanded: boolean]
}>()

interface Props {
  node: ISysDept.Tree
  level: number
  selectedIds: number[]
  multiple: boolean
  checkStrictly: boolean
  expandedNodes?: Set<number>
}

// 响应式状态
const isExpanded = ref(false)

// 监听expandedNodes变化
watch(
  () => props.expandedNodes,
  (newExpandedNodes) => {
    if (newExpandedNodes) {
      isExpanded.value = newExpandedNodes.has(props.node.id)
    }
  },
  { immediate: true },
)

// 计算属性
const hasChildren = computed(() => {
  return props.node.children && props.node.children.length > 0
})

const isSelected = computed(() => {
  return props.selectedIds.includes(props.node.id)
})

const isIndeterminate = computed(() => {
  if (!props.multiple || props.checkStrictly || !hasChildren.value) {
    return false
  }

  // 检查子节点选中状态
  const allChildIds = getAllChildIds(props.node).slice(1) // 排除自身
  const selectedChildIds = allChildIds.filter((id) => props.selectedIds.includes(id))

  // 部分子节点被选中时显示半选状态
  return selectedChildIds.length > 0 && selectedChildIds.length < allChildIds.length
})

// 工具函数
function getAllChildIds(node: ISysDept.Tree): number[] {
  const ids: number[] = [node.id]
  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      ids.push(...getAllChildIds(child))
    }
  }
  return ids
}

// 事件处理
function handleNodeClick() {
  // 如果有子节点，点击节点本身只展开/收起，不选择
  if (hasChildren.value) {
    toggleExpanded()
  } else {
    // 如果没有子节点，点击节点选择
    handleSelectClick()
  }
}

function handleSelectClick() {
  const newSelected = !isSelected.value
  emit('select', props.node, newSelected)
}

function toggleExpanded() {
  isExpanded.value = !isExpanded.value
  emit('toggle', props.node.id, isExpanded.value)
}

function handleChildSelect(node: ISysDept.Tree, selected: boolean) {
  emit('select', node, selected)
}

function handleChildToggle(nodeId: number, expanded: boolean) {
  emit('toggle', nodeId, expanded)
}
</script>

<template>
  <view class="dept-tree-node">
    <!-- 当前节点 -->
    <view
      :class="{
        'node-content': true,
        selected: isSelected,
        'has-children': hasChildren,
        indeterminate: isIndeterminate,
      }"
      @click="handleNodeClick"
    >
      <!-- 展开/收起图标 -->
      <view v-if="hasChildren" class="expand-icon" @click.stop="toggleExpanded">
        <wd-icon :name="isExpanded ? 'arrow-down' : 'arrow-right'" size="32rpx" color="#8E8E93" />
      </view>
      <!-- 选择框 -->
      <view
        class="checkbox-container"
        :style="hasChildren ? '' : 'margin-left: 48rpx'"
        @click.stop="handleSelectClick"
      >
        <view
          class="checkbox"
          :class="{
            checked: isSelected,
            indeterminate: isIndeterminate,
            multiple,
          }"
        >
          <wd-icon v-if="isSelected" name="check" size="28rpx" color="#FFFFFF" />
          <view v-else-if="isIndeterminate" class="indeterminate-mark" />
        </view>
      </view>

      <!-- 部门图标和名称 -->
      <view class="dept-info">
        <wd-icon name="home" size="36rpx" color="#007AFF" />
        <view class="dept-name">
          {{ node.name }}
        </view>
        <view v-if="node.userTotal !== undefined" class="user-count">({{ node.userTotal }}人)</view>
      </view>
    </view>

    <!-- 子节点 -->
    <view v-if="hasChildren && isExpanded" class="children-wrapper">
      <dept-tree-node
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :selected-ids="selectedIds"
        :multiple="multiple"
        :check-strictly="checkStrictly"
        :expanded-nodes="expandedNodes"
        @select="handleChildSelect"
        @toggle="handleChildToggle"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统变量
$system-blue: #007aff;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;
$label-primary: rgba(0, 0, 0, 0.85);
$label-secondary: rgba(0, 0, 0, 0.5);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(0, 0, 0, 0.1);

.dept-tree-node {
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  min-height: 56rpx;
  padding: 8rpx 10rpx;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 8rpx;
  margin: 4rpx 0;

  &:active {
    background-color: $system-gray6;
    transform: scale(0.98);
  }

  &.selected {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.08) 0%, rgba(0, 122, 255, 0.04) 100%);
    border: 2rpx solid rgba(0, 122, 255, 0.2);
  }

  &.indeterminate {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(0, 122, 255, 0.02) 100%);
  }
}

.expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:active {
    background-color: rgba(0, 0, 0, 0.08);
    transform: scale(0.9);
  }
}

.checkbox-container {
  margin-right: 10rpx;
}

.checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid $system-gray3;
  border-radius: 6rpx;
  background-color: $background-primary;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);

  &.multiple {
    border-radius: 6rpx;
  }

  &.checked {
    border-color: $system-blue;
    background: linear-gradient(135deg, $system-blue 0%, #5ac8fa 100%);
    box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  }

  &.indeterminate {
    border-color: $system-blue;
    background: linear-gradient(135deg, $system-blue 0%, #5ac8fa 100%);
    box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
  }

  &:active {
    transform: scale(0.9);
  }
}

.indeterminate-mark {
  width: 16rpx;
  height: 4rpx;
  background-color: $background-primary;
  border-radius: 2rpx;
}

.dept-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12rpx;
}

.dept-name {
  font-size: 28rpx;
  color: $label-primary;
  font-weight: 500;
  letter-spacing: -0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.user-count {
  font-size: 24rpx;
  color: $label-secondary;
  margin-left: 8rpx;
  font-weight: 400;
}

.children-wrapper {
  border-left: 2rpx solid $system-gray6;
  margin-left: 20rpx;
  padding-left: 34rpx;
}

// 选中状态下的样式
.node-content.selected {
  .dept-name {
    color: $system-blue;
    font-weight: 600;
  }
}

// 禁用状态样式
.node-content.disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>
