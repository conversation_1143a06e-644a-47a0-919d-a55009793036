# TextShow 组件使用文档

## 概述

TextShow 是一个通用的文本展示组件，用于根据后端配置动态展示不同类型的文本内容。组件会根据传入的 `textKey` 请求后端接口获取数据，并根据响应的 `textType` 和 `businessType` 字段展示不同的UI样式和交互行为。

## 功能特性

- 🚀 **动态加载**: 根据 textKey 动态请求后端数据
- 🎨 **多种样式**: 支持公告、协议、信息、格子等多种业务类型
- 📝 **富文本支持**: 支持普通文本、富文本、Markdown 等文本类型
- 💾 **智能缓存**: 支持数据缓存，避免重复请求
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🔗 **链接跳转**: 支持内部页面跳转和外部链接处理

## 基本用法

```vue
<template>
  <view>
    <!-- 基本使用 -->
    <TextShow text-key="user-agreement" />
    
    <!-- 自定义样式 -->
    <TextShow 
      text-key="privacy-policy" 
      custom-class="my-text-show"
      :show-loading="false"
    />
    
    <!-- 监听事件 -->
    <TextShow 
      text-key="announcement" 
      @loaded="onTextLoaded"
      @error="onTextError"
      @click="onTextClick"
    />
  </view>
</template>

<script setup lang="ts">
import TextShow from '@/components/common/meta/TextShow.vue'
import type { IMetaText } from '@/api/interface/system/meta/mateText'

function onTextLoaded(data: IMetaText.MetaText) {
  console.log('文本加载完成:', data)
}

function onTextError(error: any) {
  console.error('文本加载失败:', error)
}

function onTextClick(data: IMetaText.MetaText) {
  console.log('文本被点击:', data)
}
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| textKey | string | - | ✅ | 文本的唯一标识key |
| showLoading | boolean | true | ❌ | 是否显示加载状态 |
| customClass | string | '' | ❌ | 自定义样式类名 |
| cache | boolean | true | ❌ | 是否缓存数据 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| loaded | (data: IMetaText.MetaText) | 数据加载完成时触发 |
| error | (error: any) | 数据加载失败时触发 |
| click | (data: IMetaText.MetaText) | 文本被点击时触发 |

## 暴露的方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refresh | - | Promise<void> | 刷新数据 |
| textData | - | Ref<IMetaText.MetaText \| null> | 获取文本数据 |
| loading | - | Ref<boolean> | 获取加载状态 |

```vue
<template>
  <TextShow ref="textShowRef" text-key="example" />
  <button @click="refreshData">刷新数据</button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TextShow from '@/components/common/meta/TextShow.vue'

const textShowRef = ref()

function refreshData() {
  textShowRef.value?.refresh()
}
</script>
```

## 业务类型说明

### 1. 公告类型 (Announcement)
- **展示方式**: 卡片样式，显示标题和内容
- **交互行为**: 点击后根据是否有跳转URL决定行为
- **适用场景**: 系统公告、通知消息

### 2. 协议类型 (Agreement)
- **展示方式**: 列表项样式，只显示标题
- **交互行为**: 点击后弹窗显示完整内容
- **适用场景**: 用户协议、隐私政策

### 3. 格子类型 (Cell)
- **展示方式**: 带图标的卡片样式
- **交互行为**: 点击后弹窗显示详细信息
- **适用场景**: 功能入口、信息展示

### 4. 信息类型 (Information)
- **展示方式**: 信息提示样式
- **交互行为**: 点击后根据是否有跳转URL决定行为
- **适用场景**: 提示信息、帮助说明

## 文本类型说明

### 1. 普通文本 (NormalText)
- 直接显示文本内容
- 支持换行和基本格式

### 2. 富文本 (RichText)
- 支持HTML标签
- 自动处理图片、链接等元素

### 3. Markdown (Markdown)
- 支持Markdown语法
- 自动转换为HTML显示

## 样式定制

```scss
// 自定义样式示例
.my-text-show {
  // 公告样式定制
  .announcement-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  // 协议样式定制
  .agreement-container {
    .agreement-title {
      color: #ff6b6b;
    }
  }
  
  // 格子样式定制
  .cell-container {
    .cell-item {
      border: 2px solid #4ecdc4;
    }
  }
}
```

## 后端数据结构

```typescript
interface MetaText {
  id: number
  name: string           // 文本名字
  textKey: string        // 文本key（唯一标识）
  textType: string       // 文本类型：NormalText | RichText | Markdown
  icon?: string          // 图标名称
  businessType: string   // 业务类型：Announcement | Agreement | Cell | Information
  title?: string         // 文本标题
  skipUrl?: string       // 跳转URL
  content?: string       // 文本内容
  sort?: number          // 排序
  enable: boolean        // 是否启用
  createTime?: string    // 创建时间
  updateTime?: string    // 更新时间
}
```

## 注意事项

1. **textKey 必须唯一**: 确保每个文本内容都有唯一的 textKey
2. **启用状态检查**: 组件只会显示 `enable: true` 的文本内容
3. **缓存机制**: 默认开启缓存，相同 textKey 的数据会被缓存
4. **跳转处理**: 外部链接在小程序中会复制到剪贴板
5. **富文本安全**: 使用富文本时注意内容安全性

## 最佳实践

1. **合理使用缓存**: 对于不经常变化的内容开启缓存
2. **错误处理**: 监听 error 事件处理加载失败的情况
3. **加载状态**: 对于加载时间较长的内容显示加载状态
4. **响应式设计**: 使用 customClass 适配不同场景的样式需求
