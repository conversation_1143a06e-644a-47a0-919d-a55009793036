<script setup lang="ts">
interface Category {
  id: number
  name: string
  parentId?: number
  children?: Category[]
}

interface Props {
  modelValue?: number | string
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  size?: 'large' | 'default' | 'small'
}

defineOptions({
  name: 'CategorySelect',
})

const modelValue = defineModel<string | number>()

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择分类',
  clearable: true,
  size: 'default',
})

// 响应式数据
const categories = ref<Category[]>([])
const loading = ref(false)

// 计算属性
const categoryOptions = computed(() => {
  return categories.value.map((category) => ({
    value: category.id,
    label: category.name,
    children: category.children?.map((child) => ({
      value: child.id,
      label: child.name,
    })),
  }))
})

const displayText = computed(() => {
  if (!modelValue.value) return ''

  const findCategory = (cats: Category[], id: number | string): Category | undefined => {
    for (const cat of cats) {
      if (cat.id == id) return cat
      if (cat.children) {
        const found = findCategory(cat.children, id)
        if (found) return found
      }
    }
    return undefined
  }

  const category = findCategory(categories.value, modelValue.value)
  return category?.name || ''
})

// 事件处理
function handleSelect(value: any) {
  modelValue.value = value[0]

  const findCategory = (cats: Category[], id: number | string): Category | undefined => {
    for (const cat of cats) {
      if (cat.id == id) return cat
      if (cat.children) {
        const found = findCategory(cat.children, id)
        if (found) return found
      }
    }
    return undefined
  }
}

function handleClear() {
  modelValue.value = undefined
}

// 获取分类列表
async function loadCategories() {
  loading.value = true
  try {
    // TODO: 调用实际的API接口获取分类列表
    // const response = await getCategoryListApi()
    // categories.value = response.data || []

    // 暂时使用模拟数据
    categories.value = [
      {
        id: 1,
        name: '医疗器械',
        children: [
          { id: 11, name: '监护设备', parentId: 1 },
          { id: 12, name: '手术器械', parentId: 1 },
        ],
      },
      {
        id: 2,
        name: '防护用品',
        children: [
          { id: 21, name: '口罩', parentId: 2 },
          { id: 22, name: '防护服', parentId: 2 },
        ],
      },
      {
        id: 3,
        name: '药品耗材',
        children: [
          { id: 31, name: '急救药品', parentId: 3 },
          { id: 32, name: '医用耗材', parentId: 3 },
        ],
      },
    ]
  } catch (error) {
    console.error('获取分类列表失败:', error)
    uni.showToast({
      title: '获取分类列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})
</script>

<template>
  <view>
    <!-- 外层view组件不能删除，否则微信小程样式会不生效！！！ -->
    <wd-select-picker
      v-bind="{ ...$attrs, ...props }"
      v-model="modelValue"
      :columns="categoryOptions"
      @confirm="handleSelect"
      @clear="handleClear"
    >
      <wd-cell
        :title="displayText || placeholder"
        :value="displayText"
        :placeholder="placeholder"
        :disabled="disabled"
        is-link
      />
    </wd-select-picker>
  </view>
</template>

<style lang="scss" scoped>
// Apple 风格样式
:deep(.wd-cell) {
  padding: 28rpx 32rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    border-color: #007aff;
    box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
  }

  &.is-disabled {
    opacity: 0.6;
    background-color: #f2f2f7;
  }
}

:deep(.wd-cell__title) {
  color: #1d1d1f;
  font-weight: 500;
  font-size: 32rpx;
}

:deep(.wd-cell__value) {
  color: #8e8e93;
  font-size: 30rpx;
}
</style>
