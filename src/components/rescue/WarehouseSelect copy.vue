<script setup lang="ts">
import type { IMetaWarehouse } from '@/api/interface/rescue/meta/metaWarehouse'
import { getMetaWarehouseListApi } from '@/api/modules/rescue/meta/metaWarehouse'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  placeholder: '请选择仓库',
  clearable: true,
  frequentWarehouseIds: () => [],
  maxWarehouseCount: 10,
  size: 'default',
  disabled: false,
  modelValue: undefined,
  showSelectedWarehouses: false,
})

const emit = defineEmits(['update:modelValue'])

interface Props {
  modelValue?: number | number[]
  multiple?: boolean
  placeholder?: string
  clearable?: boolean
  frequentWarehouseIds?: number[]
  maxWarehouseCount?: number
  size?: 'small' | 'default' | 'large'
  disabled?: boolean
  showSelectedWarehouses?: boolean
}

// ============ 状态管理 ============
const loading = ref(false)
const showWarehousePicker = ref(false)

// ============ 数据状态 ============
const warehouseOptions = ref<IMetaWarehouse.Row[]>([])
const frequentWarehouses = ref<IMetaWarehouse.Row[]>([])
const selectedWarehouses = ref<number | number[]>(props.modelValue ?? (props.multiple ? [] : 0))
const searchQuery = ref('')

// ============ 分页相关 ============
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMoreData = ref(false)

// ============ 计算属性 ============
const selectedWarehousesData = computed(() => {
  if (
    !selectedWarehouses.value ||
    (Array.isArray(selectedWarehouses.value) && selectedWarehouses.value.length === 0)
  ) {
    return []
  }

  const ids = Array.isArray(selectedWarehouses.value)
    ? selectedWarehouses.value
    : [selectedWarehouses.value]
  const allWarehouses = [...warehouseOptions.value, ...frequentWarehouses.value]

  return ids
    .map((id) => {
      const warehouse = allWarehouses.find((w) => w.id === id)
      return warehouse || { id, name: `仓库${id}`, address: '' }
    })
    .filter(Boolean)
})

const warehouseSelectorText = computed(() => {
  const count = selectedWarehousesData.value.length
  if (count === 0) return props.placeholder
  if (props.multiple) {
    return count === 1 ? selectedWarehousesData.value[0].name : `已选 ${count} 个仓库`
  }
  return selectedWarehousesData.value[0]?.name || props.placeholder
})

const sizeClass = computed(() => {
  const sizeMap = {
    small: 'warehouse-select--small',
    default: 'warehouse-select--default',
    large: 'warehouse-select--large',
  }
  return sizeMap[props.size]
})

// ============ 监听器 ============
watch(
  () => props.modelValue,
  (newVal) => {
    selectedWarehouses.value = newVal ?? (props.multiple ? [] : 0)
  },
  { immediate: true },
)

// ============ 数据加载函数 ============
async function loadWarehouseData() {
  loading.value = true
  try {
    const params: any = {
      enable: 'true',
      page: currentPage.value,
      limit: pageSize.value,
    }

    if (searchQuery.value && searchQuery.value.trim()) {
      params.name = searchQuery.value.trim()
    }

    const res = await getMetaWarehouseListApi(params)
    console.log('仓库数据响应:', res) // 调试日志

    if (currentPage.value === 1) {
      warehouseOptions.value = (res.data as any)?.rows || []
    } else {
      warehouseOptions.value = [...warehouseOptions.value, ...((res.data as any)?.rows || [])]
    }

    total.value = (res.data as any)?.total || 0
    hasMoreData.value = warehouseOptions.value.length < total.value

    console.log('仓库选项:', warehouseOptions.value) // 调试日志
  } catch (error) {
    console.error('获取仓库列表失败:', error)
    uni.showToast({
      title: '获取仓库列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

async function loadFrequentWarehouses() {
  if (!props.frequentWarehouseIds?.length) return

  try {
    const res = await getMetaWarehouseListApi({
      page: 1,
      limit: 100,
      enable: 'true',
    })

    if ((res.data as any)?.rows?.length) {
      frequentWarehouses.value = (res.data as any).rows.filter((warehouse: any) =>
        props.frequentWarehouseIds.includes(Number(warehouse.id)),
      )
    }
  } catch (error) {
    console.error('获取常用仓库失败:', error)
  }
}

async function loadMoreData() {
  if (loading.value || !hasMoreData.value) return
  currentPage.value++
  await loadWarehouseData()
}

// ============ 事件处理函数 ============
function handleSearch() {
  currentPage.value = 1
  loadWarehouseData()
}

function clearSearch() {
  searchQuery.value = ''
  handleSearch()
}

function toggleWarehouse(warehouseId: number) {
  if (props.multiple) {
    const ids = Array.isArray(selectedWarehouses.value) ? selectedWarehouses.value : []
    const index = ids.indexOf(warehouseId)

    if (index > -1) {
      ids.splice(index, 1)
    } else {
      if (ids.length < props.maxWarehouseCount) {
        ids.push(warehouseId)
      } else {
        uni.showToast({
          title: `最多选择${props.maxWarehouseCount}个仓库`,
          icon: 'none',
        })
        return
      }
    }
    selectedWarehouses.value = ids
  } else {
    selectedWarehouses.value = warehouseId
    showWarehousePicker.value = false
  }

  emit('update:modelValue', selectedWarehouses.value)
}

function removeWarehouse(warehouseId: number) {
  if (props.multiple) {
    const ids = Array.isArray(selectedWarehouses.value) ? selectedWarehouses.value : []
    const index = ids.indexOf(warehouseId)
    if (index > -1) {
      ids.splice(index, 1)
      selectedWarehouses.value = ids
    }
  } else {
    selectedWarehouses.value = 0
  }

  emit('update:modelValue', selectedWarehouses.value)
}

function isWarehouseSelected(warehouseId: number) {
  if (props.multiple) {
    return Array.isArray(selectedWarehouses.value) && selectedWarehouses.value.includes(warehouseId)
  } else {
    return selectedWarehouses.value === warehouseId
  }
}

function clearSelection() {
  if (props.multiple) {
    selectedWarehouses.value = []
  } else {
    selectedWarehouses.value = 0
  }

  emit('update:modelValue', selectedWarehouses.value)
}

// ============ 初始化 ============
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([loadFrequentWarehouses(), loadWarehouseData()])
  } catch (error) {
    console.error('初始化仓库选择器失败:', error)
  } finally {
    loading.value = false
  }
})

// ============ 暴露给父组件的方法 ============
defineExpose({
  clearSelection,
})
</script>

<template>
  <view
    class="warehouse-select-container"
    :class="[sizeClass, { 'warehouse-select--disabled': disabled }]"
  >
    <!-- 主选择器 -->
    <view class="main-selector">
      <view
        class="warehouse-trigger"
        :class="{ 'warehouse-trigger--active': showWarehousePicker }"
        @click="!disabled && (showWarehousePicker = true)"
      >
        <view class="trigger-content">
          <wd-icon name="home" class="trigger-icon" />
          <view
            class="trigger-text"
            :class="{ 'trigger-text--placeholder': selectedWarehousesData.length === 0 }"
          >
            {{ warehouseSelectorText }}
          </view>
        </view>
        <view class="trigger-actions">
          <wd-icon
            v-if="selectedWarehousesData.length > 0 && props.clearable"
            name="close"
            size="16"
            class="trigger-clear"
            @click.stop="clearSelection"
          />
          <wd-icon
            name="arrow-down"
            size="16"
            class="trigger-arrow"
            :class="{ 'trigger-arrow--active': showWarehousePicker }"
          />
        </view>
      </view>
    </view>

    <!-- 已选仓库显示 -->
    <view
      v-if="selectedWarehousesData.length > 0 && showSelectedWarehouses"
      class="selected-warehouses-display"
    >
      <!-- 多选模式下的清空按钮 -->
      <view v-if="props.multiple" class="clear-selection-section">
        <view class="selected-count">已选择 {{ selectedWarehousesData.length }} 个仓库</view>
        <wd-button
          type="text"
          size="small"
          icon="delete"
          custom-class="clear-selection-btn"
          @click="clearSelection"
        >
          清空
        </wd-button>
      </view>

      <!-- 单选模式下的已选仓库 -->
      <view v-if="!props.multiple" class="single-selection-display">
        <view class="selected-warehouse-info">
          <wd-icon name="home" class="info-icon" />
          <view class="info-content">
            <view class="info-title">已选择仓库</view>
            <view class="info-name">
              {{ selectedWarehousesData[0]?.name }}
            </view>
            <view v-if="selectedWarehousesData[0]?.address" class="info-address">
              {{ selectedWarehousesData[0]?.address }}
            </view>
          </view>
          <wd-button
            v-if="props.clearable"
            type="text"
            size="small"
            icon="delete"
            custom-class="clear-selection-btn"
            @click="clearSelection"
          >
            清空
          </wd-button>
        </view>
      </view>

      <!-- 多选模式下的已选仓库列表 -->
      <template v-if="props.multiple">
        <view
          v-for="warehouse in selectedWarehousesData"
          :key="warehouse.id"
          class="selected-warehouse-chip"
          @click="removeWarehouse(warehouse.id)"
        >
          <wd-icon name="home" class="chip-icon" />
          <view class="chip-content">
            <view class="chip-name">
              {{ warehouse.name }}
            </view>
            <view v-if="warehouse.address" class="chip-address">
              {{ warehouse.address }}
            </view>
          </view>
          <view class="chip-remove">×</view>
        </view>
      </template>
    </view>

    <!-- 仓库选择弹窗 -->
    <wd-popup
      v-model="showWarehousePicker"
      position="bottom"
      :close-on-click-modal="true"
      custom-class="warehouse-picker-popup"
      :safe-area-inset-bottom="true"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="99999"
    >
      <view class="picker-container">
        <!-- 头部 -->
        <view class="picker-header">
          <view class="picker-header-content">
            <view class="picker-header-left">
              <wd-button
                type="text"
                size="small"
                icon="close"
                custom-class="picker-close-btn"
                @click="showWarehousePicker = false"
              />
            </view>
            <view class="picker-title">选择仓库</view>
            <view class="picker-header-right">
              <wd-button
                type="primary"
                size="small"
                custom-class="picker-done-btn"
                @click="showWarehousePicker = false"
              >
                完成
              </wd-button>
            </view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="picker-search">
          <view class="search-input-wrapper">
            <wd-icon name="search" size="16" class="search-icon" />
            <input
              v-model="searchQuery"
              class="search-input"
              placeholder="搜索仓库名称或地址..."
              @input="handleSearch"
            />
            <wd-icon
              v-if="searchQuery"
              name="close"
              size="16"
              class="search-clear"
              @click="clearSearch"
            />
          </view>
        </view>

        <!-- 仓库列表 -->
        <scroll-view class="picker-content" scroll-y @scrolltolower="loadMoreData">
          <!-- 常用仓库 -->
          <view v-if="frequentWarehouses.length > 0" class="warehouse-section">
            <view class="section-title">常用仓库</view>
            <view class="warehouse-grid">
              <view
                v-for="warehouse in frequentWarehouses"
                :key="`frequent-${warehouse.id}`"
                class="warehouse-item"
                :class="{ 'warehouse-item--selected': isWarehouseSelected(warehouse.id) }"
                @click="toggleWarehouse(warehouse.id)"
              >
                <view class="warehouse-content">
                  <view class="warehouse-header">
                    <wd-icon name="home" class="warehouse-icon" />
                    <view class="warehouse-name">
                      {{ warehouse.name }}
                    </view>
                    <wd-icon
                      v-if="isWarehouseSelected(warehouse.id)"
                      name="check"
                      size="16"
                      class="warehouse-check"
                    />
                  </view>
                  <view v-if="warehouse.address" class="warehouse-address">
                    {{ warehouse.address }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 所有仓库 -->
          <view v-if="warehouseOptions.length > 0" class="warehouse-section">
            <view class="section-title">
              {{ frequentWarehouses.length ? '全部仓库' : '仓库列表' }}
            </view>
            <view class="warehouse-grid">
              <view
                v-for="warehouse in warehouseOptions"
                :key="`all-${warehouse.id}`"
                class="warehouse-item"
                :class="{ 'warehouse-item--selected': isWarehouseSelected(warehouse.id) }"
                @click="toggleWarehouse(warehouse.id)"
              >
                <view class="warehouse-content">
                  <view class="warehouse-header">
                    <wd-icon name="home" class="warehouse-icon" />
                    <view class="warehouse-name">
                      {{ warehouse.name }}
                    </view>
                    <wd-icon
                      v-if="isWarehouseSelected(warehouse.id)"
                      name="check"
                      size="16"
                      class="warehouse-check"
                    />
                  </view>
                  <view v-if="warehouse.address" class="warehouse-address">
                    {{ warehouse.address }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <view v-if="hasMoreData" class="load-more-container">
            <view v-if="!loading" class="load-more-btn" @click="loadMoreData">
              <view class="load-more-text">加载更多</view>
              <wd-icon name="arrow-down" size="16" class="load-more-icon" />
            </view>
            <view v-else class="loading-indicator">
              <wd-loading size="32rpx" />
              <view class="loading-text">加载中...</view>
            </view>
          </view>

          <!-- 空状态 -->
          <view
            v-if="!loading && warehouseOptions.length === 0 && frequentWarehouses.length === 0"
            class="empty-state"
          >
            <wd-icon name="home" size="48" class="empty-icon" />
            <view class="empty-text">
              {{ searchQuery ? '暂无匹配仓库' : '暂无仓库数据' }}
            </view>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
// Apple 设计系统变量
:root {
  --apple-blue: #007aff;
  --apple-blue-light: #5ac8fa;
  --apple-blue-dark: #0056cc;
  --apple-gray: #8e8e93;
  --apple-gray-light: #c7c7cc;
  --apple-gray-dark: #48484a;
  --apple-red: #ff3b30;
  --apple-green: #34c759;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-radius: 24rpx;
  --apple-radius-small: 16rpx;
  --apple-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  --apple-shadow-elevated: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.warehouse-select-container {
  width: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  // 尺寸变化
  &--small {
    font-size: 28rpx;

    .warehouse-trigger {
      min-height: 80rpx;
      padding: 16rpx 24rpx;
    }
  }

  &--default {
    font-size: 32rpx;
  }

  &--large {
    font-size: 36rpx;

    .warehouse-trigger {
      min-height: 112rpx;
      padding: 32rpx 40rpx;
    }
  }

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  // 主选择器样式
  .main-selector {
    .warehouse-trigger {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx 40rpx;
      background-color: var(--apple-surface);
      border: 2rpx solid rgba(0, 0, 0, 0.08);
      border-radius: 24rpx;
      min-height: 104rpx;
      box-sizing: border-box;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      user-select: none;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

      .trigger-content {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 20rpx;
      }

      .trigger-icon {
        color: var(--apple-blue);
        font-size: 40rpx;
      }

      .trigger-text {
        font-size: 32rpx;
        color: #1d1d1f;
        line-height: 1.4;
        font-weight: 500;

        &--placeholder {
          color: var(--apple-gray);
          font-weight: 400;
        }
      }

      .trigger-actions {
        display: flex;
        align-items: center;
        gap: 16rpx;
      }

      .trigger-clear {
        color: var(--apple-gray);
        transition: all 0.2s ease;
        cursor: pointer;
        padding: 8rpx;
        border-radius: 50%;

        &:hover {
          color: var(--apple-gray-dark);
          background-color: rgba(0, 0, 0, 0.05);
        }

        &:active {
          transform: scale(0.9);
        }
      }

      .trigger-arrow {
        color: var(--apple-gray);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &--active {
          transform: rotate(180deg);
          color: var(--apple-blue);
        }
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
      }

      &--active {
        border-color: var(--apple-blue);
        box-shadow: 0 8rpx 40rpx rgba(0, 122, 255, 0.15);
      }
    }
  }

  // 已选仓库显示
  .selected-warehouses-display {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-top: 32rpx;

    .clear-selection-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 32rpx;
      background-color: rgba(142, 142, 147, 0.08);
      border-radius: 20rpx;
      margin-bottom: 8rpx;

      .selected-count {
        font-size: 28rpx;
        font-weight: 500;
        color: var(--apple-gray);
      }

      :deep(.clear-selection-btn) {
        display: flex;
        align-items: center;
        gap: 12rpx;
        padding: 12rpx 24rpx;
        background-color: rgba(255, 59, 48, 0.1);
        border-radius: 36rpx;
        border: none;
        transition: all 0.2s ease;
        font-size: 26rpx;
        font-weight: 500;
        color: var(--apple-red);

        &:active {
          background-color: rgba(255, 59, 48, 0.2);
          transform: scale(0.95);
        }
      }
    }

    .single-selection-display {
      .selected-warehouse-info {
        display: flex;
        align-items: center;
        padding: 32rpx 36rpx;
        background-color: rgba(0, 122, 255, 0.08);
        border: 2rpx solid rgba(0, 122, 255, 0.2);
        border-radius: 24rpx;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        .info-icon {
          color: var(--apple-blue);
          margin-right: 24rpx;
          font-size: 40rpx;
        }

        .info-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4rpx;

          .info-title {
            font-size: 26rpx;
            font-weight: 500;
            color: var(--apple-gray);
            line-height: 1.2;
          }

          .info-name {
            font-size: 32rpx;
            font-weight: 600;
            color: var(--apple-blue);
            line-height: 1.3;
          }

          .info-address {
            font-size: 28rpx;
            color: var(--apple-gray);
            line-height: 1.2;
          }
        }

        :deep(.clear-selection-btn) {
          display: flex;
          align-items: center;
          gap: 12rpx;
          padding: 12rpx 24rpx;
          background-color: rgba(255, 59, 48, 0.1);
          border-radius: 36rpx;
          border: none;
          transition: all 0.2s ease;

          &:active {
            background-color: rgba(255, 59, 48, 0.2);
            transform: scale(0.95);
          }

          wd-icon {
            color: var(--apple-red);
          }

          .clear-text {
            font-size: 26rpx;
            font-weight: 500;
            color: var(--apple-red);
          }
        }
      }
    }

    .selected-warehouse-chip {
      display: flex;
      align-items: center;
      padding: 28rpx 36rpx;
      background-color: rgba(0, 122, 255, 0.08);
      border: 2rpx solid rgba(0, 122, 255, 0.2);
      border-radius: 24rpx;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;
      overflow: hidden;

      &:active {
        transform: scale(0.96);
        background-color: rgba(0, 122, 255, 0.12);
      }

      .chip-icon {
        color: var(--apple-blue);
        margin-right: 20rpx;
        font-size: 36rpx;
      }

      .chip-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6rpx;

        .chip-name {
          font-size: 30rpx;
          font-weight: 600;
          color: var(--apple-blue);
          line-height: 1.3;
        }

        .chip-address {
          font-size: 26rpx;
          color: var(--apple-gray);
          line-height: 1.2;
        }
      }

      .chip-remove {
        width: 48rpx;
        height: 48rpx;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: bold;
        color: var(--apple-gray);
        margin-left: 16rpx;
        transition: all 0.2s ease;

        &:active {
          background-color: rgba(255, 59, 48, 0.2);
          color: var(--apple-red);
          transform: scale(0.9);
        }
      }
    }
  }
  // 弹窗样式
  :deep(.warehouse-picker-popup) {
    border-radius: 32rpx 32rpx 0 0;
    .picker-container {
      background-color: var(--apple-surface);
      overflow: hidden;
      box-shadow: 0 -4rpx 40rpx rgba(0, 0, 0, 0.1);
      max-height: 85vh;
      padding-bottom: 100rpx; // 底部安全空间
    }

    // 确保弹窗有正确的背景遮罩
    :deep(.wd-popup__mask) {
      background-color: rgba(0, 0, 0, 0.4);
    }

    .picker-header {
      background-color: var(--apple-surface);
      border-bottom: 1rpx solid var(--apple-gray-light);

      .picker-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx 40rpx;
        position: relative;
      }

      .picker-header-left {
        width: 88rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .picker-header-right {
        width: 88rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      :deep(.picker-close-btn) {
        width: 72rpx;
        height: 72rpx;
        border-radius: 36rpx;
        background-color: rgba(142, 142, 147, 0.12);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;
        margin: 0;
        border: none;
        transition: all 0.2s ease;

        &:active {
          background-color: rgba(142, 142, 147, 0.2);
          transform: scale(0.95);
        }

        wd-icon {
          color: var(--apple-gray-dark);
        }
      }

      .picker-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-size: 34rpx;
        font-weight: 600;
        color: #1d1d1f;
        line-height: 1.4;
      }

      :deep(.picker-done-btn) {
        background-color: var(--apple-blue);
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-weight: 600;
        border: none;
        color: white;
        font-size: 30rpx;
        line-height: 1.2;
        transition: all 0.2s ease;

        &:active {
          background-color: var(--apple-blue-dark);
          transform: scale(0.95);
        }
      }
    }

    .picker-search {
      padding: 32rpx 40rpx;
      background-color: var(--apple-surface);
      border-bottom: 1rpx solid var(--apple-gray-light);

      .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background-color: rgba(142, 142, 147, 0.12);
        border-radius: 20rpx;
        border: none;
        padding: 20rpx 24rpx;
        transition: all 0.2s ease;

        &:focus-within {
          background-color: rgba(142, 142, 147, 0.16);
          transform: scale(1.02);
        }

        .search-icon {
          color: var(--apple-gray);
          margin-right: 16rpx;
        }

        .search-input {
          flex: 1;
          border: none;
          outline: none;
          font-size: 32rpx;
          color: #1d1d1f;
          background: transparent;
          font-weight: 400;

          &::placeholder {
            color: var(--apple-gray);
          }
        }

        .search-clear {
          color: var(--apple-gray);
          cursor: pointer;
          padding: 8rpx;
          margin-left: 16rpx;
          transition: all 0.2s ease;
          border-radius: 50%;

          &:active {
            background-color: rgba(142, 142, 147, 0.2);
            transform: scale(0.9);
          }
        }
      }
    }

    .picker-content {
      max-height: 50vh;
      min-height: 30vh;
      background-color: var(--apple-surface);
      padding-bottom: 40rpx;

      .warehouse-section {
        padding: 48rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

        &:last-child {
          border-bottom: none;
          padding-bottom: 64rpx;
        }

        .section-title {
          font-size: 26rpx;
          font-weight: 600;
          color: var(--apple-gray);
          margin-bottom: 32rpx;
          text-transform: uppercase;
          letter-spacing: 1.4rpx;
        }

        .warehouse-grid {
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .warehouse-item {
            background-color: var(--apple-surface);
            border-radius: 24rpx;
            border: 2rpx solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            cursor: pointer;
            user-select: none;
            overflow: hidden;

            &:active {
              transform: scale(0.96);
            }

            &--selected {
              background-color: rgba(0, 122, 255, 0.08);
              border-color: var(--apple-blue);
              box-shadow: 0 8rpx 40rpx rgba(0, 122, 255, 0.15);
            }

            .warehouse-content {
              padding: 32rpx;

              .warehouse-header {
                display: flex;
                align-items: center;
                gap: 20rpx;
                margin-bottom: 12rpx;

                .warehouse-icon {
                  color: var(--apple-blue);
                  font-size: 40rpx;
                }

                .warehouse-name {
                  flex: 1;
                  font-size: 32rpx;
                  font-weight: 600;
                  color: #1d1d1f;
                  line-height: 1.3;
                }

                .warehouse-check {
                  color: var(--apple-blue);
                  background-color: rgba(0, 122, 255, 0.1);
                  border-radius: 50%;
                  width: 48rpx;
                  height: 48rpx;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                }
              }

              .warehouse-address {
                font-size: 28rpx;
                color: var(--apple-gray);
                line-height: 1.4;
                margin-left: 60rpx;
              }
            }
          }
        }
      }

      .load-more-container {
        display: flex;
        justify-content: center;
        padding: 40rpx;

        .load-more-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 16rpx;
          padding: 24rpx 48rpx;
          background: rgba(0, 122, 255, 0.08);
          border: 2rpx solid rgba(0, 122, 255, 0.2);
          border-radius: 48rpx;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          cursor: pointer;

          &:active {
            background: rgba(0, 122, 255, 0.12);
            transform: scale(0.95);
          }

          .load-more-text {
            font-size: 30rpx;
            color: var(--apple-blue);
            font-weight: 600;
          }

          .load-more-icon {
            color: var(--apple-blue);
          }
        }

        .loading-indicator {
          display: flex;
          align-items: center;
          gap: 16rpx;
          color: var(--apple-gray);
          font-size: 30rpx;
          padding: 24rpx 48rpx;
          background: rgba(142, 142, 147, 0.08);
          border-radius: 48rpx;

          .loading-text {
            color: var(--apple-gray);
            font-weight: 500;
          }
        }
      }

      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 160rpx 40rpx;

        .empty-icon {
          color: var(--apple-gray-light);
          margin-bottom: 40rpx;
          opacity: 0.6;
        }

        .empty-text {
          font-size: 32rpx;
          color: var(--apple-gray);
          text-align: center;
          font-weight: 500;
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
