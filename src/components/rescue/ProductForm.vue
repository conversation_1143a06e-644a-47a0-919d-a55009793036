<script setup lang="ts">
import type { IProduct } from '@/api/interface/rescue/product/product'
import {
  createProductApi,
  getProductDetailApi,
  updateProductApi,
} from '@/api/modules/rescue/product/product'
import CategorySelect from '@/components/common/meta/CategorySelect.vue'
import TagSelect from '@/components/common/tag/TagSelect.vue'
import UploadImage from '@/components/common/upload/UploadImage.vue'
import WarehouseSelect from '@/components/rescue/WarehouseSelect.vue'

interface Props {
  /** 要编辑的物资数据 */
  product?: IProduct.Row
}

defineOptions({
  name: 'ProductForm',
  options: {
    styleIsolation: 'shared',
  },
})

const props = withDefaults(defineProps<Props>(), {
  product: undefined,
})

const emit = defineEmits<{
  success: []
  error: [message: string]
}>()

// 响应式数据
const isVisible = defineModel<boolean>({
  type: Boolean,
  default: false,
})

const loading = ref(false)
const submitting = ref(false)
const isEdit = ref(false)
const productId = ref<number | undefined>(undefined)

// 监听外部状态变化
watch(
  isVisible,
  (newVal) => {
    if (newVal) {
      if (props.product) {
        // 编辑模式
        isEdit.value = true
        productId.value = props.product.id
        loadProductDetail(props.product.id!)
      } else {
        // 新增模式
        isEdit.value = false
        productId.value = undefined
        resetForm()
        addSku()
      }
    }
  },
  { immediate: true },
)

// 表单数据
const formData = reactive<IProduct.ProductCreateDTO>({
  categoryId: undefined,
  warehouseId: undefined,
  name: '',
  image: '',
  productNumber: '',
  enable: true,
  tagIds: [],
  productSkus: [],
})

// 表单验证错误
const errors = ref<Record<string, string>>({})

// 计算属性
// const hasSkus = computed(() => formData.productSkus.length > 0)

// 方法
async function open(options: { isEdit: boolean; productId?: number }) {
  isVisible.value = true
  isEdit.value = options.isEdit
  productId.value = options.productId

  // 重置表单
  resetForm()

  if (options.isEdit && options.productId) {
    await loadProductDetail(options.productId)
  } else {
    // 新增时添加一个默认规格
    addSku()
  }
}

function close() {
  isVisible.value = false
  resetForm()
}

function resetForm() {
  Object.assign(formData, {
    categoryId: undefined,
    warehouseId: undefined,
    name: '',
    image: '',
    productNumber: '',
    enable: true,
    tagIds: [],
    productSkus: [],
  })
  errors.value = {}
}

// 加载物资详情
async function loadProductDetail(id: number) {
  loading.value = true
  try {
    const response = await getProductDetailApi({ id })
    if (response.data) {
      const detail = response.data

      // 设置基本信息
      formData.categoryId = detail.categoryId
      formData.warehouseId = detail.warehouseId
      formData.name = detail.name || ''
      formData.image = detail.image || ''
      formData.productNumber = detail.productNumber || ''
      formData.enable = detail.enable || true

      // 处理标签
      formData.tagIds = detail.tags?.map((tag) => tag.id) || []

      // 处理物资规格
      if (detail.productSkus && detail.productSkus.length > 0) {
        formData.productSkus = detail.productSkus.map((sku) => ({
          id: sku.id,
          name: sku.name,
          image: sku.image,
          stock: sku.stock,
          price: sku.price,
          enable: sku.enable,
          productLots:
            sku.productLots?.map((lot) => ({
              id: lot.id,
              lotNumber: lot.lotNumber || '',
              title: lot.title || '',
              stock: lot.stock || 0,
              price: lot.price || 0,
              quantity: lot.quantity || 0,
              storageDate: lot.storageDate || '',
              productDate: lot.productDate || '',
              discardDate: lot.discardDate || '',
              overhaulLastDate: lot.overhaulLastDate || '',
              expirationTime: lot.expirationTime || '',
              overhaulGap: lot.overhaulGap || 0,
              overhaulNextDate: lot.overhaulNextDate || '',
            })) || [],
        }))
      }
    }
  } catch (error) {
    console.error('获取物资详情失败:', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 规格操作
function addSku() {
  formData.productSkus.push({
    name: '',
    image: '',
    stock: 0,
    price: 0,
    enable: true,
    productLots: [],
  })
}

function removeSku(index: number) {
  if (formData.productSkus.length <= 1) {
    uni.showToast({
      title: '至少保留一个规格',
      icon: 'none',
    })
    return
  }
  formData.productSkus.splice(index, 1)
}

// 批次操作
function addLot(skuIndex: number) {
  if (!formData.productSkus[skuIndex].productLots) {
    formData.productSkus[skuIndex].productLots = []
  }
  formData.productSkus[skuIndex].productLots!.push({
    lotNumber: '',
    title: '',
    stock: 0,
    price: 0,
    quantity: 0,
    storageDate: '',
    productDate: '',
    discardDate: '',
    overhaulLastDate: '',
    expirationTime: '',
    overhaulGap: 0,
    overhaulNextDate: '',
  })
}

function removeLot(skuIndex: number, lotIndex: number) {
  if (formData.productSkus[skuIndex].productLots) {
    formData.productSkus[skuIndex].productLots!.splice(lotIndex, 1)
  }
}

// 填充测试数据
function fillTestData() {
  const currentDate = new Date().toISOString().split('T')[0]
  const nextYear = new Date()
  nextYear.setFullYear(nextYear.getFullYear() + 1)
  const nextYearDate = nextYear.toISOString().split('T')[0]

  Object.assign(formData, {
    categoryId: 1,
    warehouseId: 1,
    name: `测试物资-${Date.now()}`,
    image: `https://dummyimage.com/200x200/007AFF/fff&text=测试物资`,
    productNumber: `TEST-${Date.now()}`,
    enable: true,
    tagIds: [1, 2],
    productSkus: [
      {
        name: '标准规格',
        image: `https://dummyimage.com/150x150/34C759/fff&text=标准`,
        stock: 100,
        price: 299.99,
        enable: true,
        productLots: [
          {
            lotNumber: `LOT-${Date.now()}`,
            title: '2024年春季批次',
            stock: 50,
            price: 299.99,
            quantity: 50,
            storageDate: currentDate,
            productDate: currentDate,
            discardDate: nextYearDate,
            overhaulLastDate: currentDate,
            expirationTime: nextYearDate,
            overhaulGap: 30,
            overhaulNextDate: nextYearDate,
          },
        ],
      },
    ],
  })

  uni.showToast({
    title: '测试数据已填充',
    icon: 'success',
  })
}

// 表单验证
function validateForm() {
  errors.value = {}

  if (!formData.name?.trim()) {
    errors.value.name = '请输入物资名称'
  }

  if (!formData.categoryId) {
    errors.value.categoryId = '请选择分类'
  }

  if (!formData.warehouseId) {
    errors.value.warehouseId = '请选择仓库'
  }

  if (formData.productSkus.length === 0) {
    uni.showToast({
      title: '至少添加一个规格',
      icon: 'none',
    })
    return false
  }

  return Object.keys(errors.value).length === 0
}

// 复制物资编号
function copyProductNumber(productNumber: string) {
  uni.setClipboardData({
    data: productNumber,
    success: () => {
      uni.showToast({
        title: '编号已复制',
        icon: 'success',
        duration: 1500,
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 1500,
      })
    },
  })
}

// 提交表单
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  submitting.value = true
  try {
    const submitData = { ...formData }

    // 如果是编辑模式，添加ID
    if (isEdit.value && productId.value) {
      submitData.id = productId.value
    }

    if (isEdit.value) {
      await updateProductApi(submitData)
      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      await createProductApi(submitData)
      uni.showToast({
        title: '创建成功',
        icon: 'success',
      })
    }

    emit('success')
    close()
  } catch (error) {
    console.error('提交失败:', error)
    const errorMessage = '提交失败'
    uni.showToast({
      title: errorMessage,
      icon: 'none',
    })
    emit('error', errorMessage)
  } finally {
    submitting.value = false
  }
}

// 暴露方法
defineExpose({
  open,
  close,
  copyProductNumber,
})
</script>

<template>
  <view>
    <!-- 外层view组件不能删除，否则微信小程样式会不生效！！！ -->
    <wd-popup
      v-model="isVisible"
      position="bottom"
      :close-on-click-modal="false"
      :safe-area-inset-bottom="true"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="99999"
      custom-class="product-form-popup"
    >
      <view class="form-container">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-overlay">
          <view class="loading-content">
            <wd-loading size="24" color="#007AFF" />
            <view class="loading-text">加载中...</view>
          </view>
        </view>
        <!-- 头部 -->
        <view class="form-header">
          <view class="header-content">
            <view class="header-title">
              {{ isEdit ? '编辑物资' : '新增物资' }}
            </view>
            <view class="header-actions">
              <wd-button type="text" size="small" custom-class="test-btn" @click="fillTestData">
                测试数据
              </wd-button>
              <wd-icon name="close" size="20" class="close-btn" @click="close" />
            </view>
          </view>
        </view>

        <!-- 表单内容 -->
        <scroll-view class="form-content" scroll-y :show-scrollbar="false">
          <!-- 基本信息 -->
          <view class="form-section">
            <view class="section-header">
              <wd-icon name="info" size="16" color="#007AFF" />
              <view class="section-title">基本信息</view>
            </view>

            <view class="form-fields">
              <!-- 物资图片 -->
              <view class="field-group">
                <view class="field-label">物资图片</view>
                <UploadImage
                  v-model:image-url="formData.image"
                  :limit="1"
                  width="120px"
                  height="120px"
                  class="image-upload"
                />
              </view>

              <!-- 物资名称 -->
              <view class="field-group">
                <view class="field-label required">物资名称</view>
                <wd-input
                  v-model="formData.name"
                  placeholder="请输入物资名称"
                  clearable
                  no-border
                  :error="!!errors.name"
                  :error-message="errors.name"
                  custom-class="form-input"
                />
              </view>

              <!-- 物资编号 -->
              <view class="field-group">
                <view class="field-label">物资编号</view>
                <view class="product-number-wrapper">
                  <wd-input
                    v-model="formData.productNumber"
                    placeholder="请输入物资编号"
                    clearable
                    no-border
                    custom-class="form-input"
                  />
                  <wd-button
                    v-if="formData.productNumber"
                    type="text"
                    size="small"
                    icon="copy"
                    custom-class="copy-btn"
                    @click="copyProductNumber(formData.productNumber)"
                  >
                    复制
                  </wd-button>
                </view>
              </view>

              <!-- 分类选择 -->
              <view class="field-group">
                <view class="field-label required">分类</view>
                <CategorySelect
                  v-model="formData.categoryId"
                  placeholder="请选择物资分类"
                  :error="!!errors.categoryId"
                  class="form-select"
                />
                <view v-if="errors.categoryId" class="error-text">
                  {{ errors.categoryId }}
                </view>
              </view>

              <!-- 仓库选择 -->
              <view class="field-group">
                <view class="field-label required">仓库</view>
                <WarehouseSelect
                  v-model="formData.warehouseId"
                  placeholder="请选择物资仓库"
                  :error="!!errors.warehouseId"
                  class="form-select"
                />
                <view v-if="errors.warehouseId" class="error-text">
                  {{ errors.warehouseId }}
                </view>
              </view>

              <!-- 标签选择 -->
              <view class="field-group">
                <view class="field-label">标签</view>
                <TagSelect
                  v-model="formData.tagIds"
                  :multiple="true"
                  placeholder="请选择标签"
                  class="form-select"
                />
              </view>

              <!-- 启用状态 -->
              <view class="field-group">
                <view class="field-label">启用状态</view>
                <wd-switch
                  v-model="formData.enable"
                  active-text="启用"
                  inactive-text="禁用"
                  custom-class="form-switch"
                />
              </view>
            </view>
          </view>

          <!-- 规格信息 -->
          <view class="form-section">
            <view class="section-header">
              <wd-icon name="grid" size="16" color="#34C759" />
              <view class="section-title">规格信息</view>
              <wd-button
                type="primary"
                icon="plus"
                size="small"
                custom-class="add-btn"
                @click="addSku"
              >
                添加规格
              </wd-button>
            </view>

            <view class="sku-list">
              <view
                v-for="(sku, skuIndex) in formData.productSkus"
                :key="skuIndex"
                class="sku-card"
              >
                <!-- 规格头部 -->
                <view class="sku-header">
                  <view class="sku-title">规格 {{ skuIndex + 1 }}</view>
                  <wd-button
                    type="error"
                    size="small"
                    icon="delete"
                    custom-class="remove-btn"
                    @click="removeSku(skuIndex)"
                  />
                </view>

                <!-- 规格信息 -->
                <view class="sku-fields">
                  <!-- 规格图片 -->
                  <view class="field-row">
                    <view class="field-group">
                      <view class="field-label">规格图片</view>
                      <UploadImage
                        v-model:image-url="sku.image"
                        :limit="1"
                        width="160rpx"
                        height="160rpx"
                        class="sku-image-upload"
                      />
                    </view>
                  </view>

                  <!-- 规格名称和库存 -->
                  <view class="field-row">
                    <view class="field-group">
                      <view class="field-label">规格名称</view>
                      <wd-input
                        v-model="sku.name"
                        placeholder="请输入规格名称"
                        clearable
                        no-border
                        custom-class="form-input"
                      />
                    </view>
                    <view class="field-group">
                      <view class="field-label">库存</view>
                      <wd-input-number
                        v-model="sku.stock"
                        :min="0"
                        placeholder="库存数量"
                        custom-class="form-number"
                      />
                    </view>
                  </view>

                  <!-- 价格和状态 -->
                  <view class="field-row">
                    <view class="field-group">
                      <view class="field-label">价格</view>
                      <wd-input-number
                        v-model="sku.price"
                        :min="0"
                        :precision="2"
                        placeholder="价格"
                        custom-class="form-number"
                      />
                    </view>
                    <view class="field-group">
                      <view class="field-label">状态</view>
                      <wd-switch
                        v-model="sku.enable"
                        active-text="启用"
                        inactive-text="禁用"
                        custom-class="sku-switch"
                      />
                    </view>
                  </view>
                </view>

                <!-- 批次信息 -->
                <view class="lots-section">
                  <view class="lots-header">
                    <view class="lots-title">批次信息</view>
                    <wd-button
                      type="primary"
                      size="small"
                      icon="plus"
                      custom-class="add-lot-btn"
                      @click="addLot(skuIndex)"
                    >
                      添加批次
                    </wd-button>
                  </view>

                  <view class="lot-list">
                    <view
                      v-for="(lot, lotIndex) in sku.productLots"
                      :key="lotIndex"
                      class="lot-card"
                    >
                      <!-- 批次头部 -->
                      <view class="lot-header">
                        <view class="lot-title">批次 {{ lotIndex + 1 }}</view>
                        <wd-button
                          type="error"
                          size="small"
                          icon="delete"
                          custom-class="remove-lot-btn"
                          @click="removeLot(skuIndex, lotIndex)"
                        />
                      </view>

                      <!-- 批次字段 -->
                      <view class="lot-fields">
                        <!-- 批次编号和名称 -->
                        <view class="field-row">
                          <view class="field-group">
                            <view class="field-label">批次编号</view>
                            <wd-input
                              v-model="lot.lotNumber"
                              placeholder="批次编号"
                              clearable
                              no-border
                              custom-class="lot-input"
                            />
                          </view>
                          <view class="field-group">
                            <view class="field-label">批次名称</view>
                            <wd-input
                              v-model="lot.title"
                              placeholder="批次名称"
                              clearable
                              no-border
                              custom-class="lot-input"
                            />
                          </view>
                        </view>

                        <!-- 库存和价格 -->
                        <view class="field-row">
                          <view class="field-group">
                            <view class="field-label">库存</view>
                            <wd-input-number
                              v-model="lot.stock"
                              :min="0"
                              placeholder="库存"
                              custom-class="lot-number"
                            />
                          </view>
                          <view class="field-group">
                            <view class="field-label">价格</view>
                            <wd-input-number
                              v-model="lot.price"
                              :min="0"
                              :precision="2"
                              placeholder="价格"
                              custom-class="lot-number"
                            />
                          </view>
                        </view>

                        <!-- 入库数量和检修间隔 -->
                        <view class="field-row">
                          <view class="field-group">
                            <view class="field-label">入库数量</view>
                            <wd-input-number
                              v-model="lot.quantity"
                              :min="0"
                              placeholder="入库数量"
                              custom-class="lot-number"
                            />
                          </view>
                          <view class="field-group">
                            <view class="field-label">检修间隔(天)</view>
                            <wd-input-number
                              v-model="lot.overhaulGap"
                              :min="0"
                              placeholder="检修间隔"
                              custom-class="lot-number"
                            />
                          </view>
                        </view>

                        <!-- 日期信息 -->
                        <view class="date-section">
                          <view class="field-row">
                            <view class="field-group">
                              <view class="field-label">生产日期</view>
                              <wd-datetime-picker
                                v-model="lot.productDate"
                                placeholder="生产日期"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                            <view class="field-group">
                              <view class="field-label">入库时间</view>
                              <wd-datetime-picker
                                v-model="lot.storageDate"
                                placeholder="入库时间"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                          </view>

                          <view class="field-row">
                            <view class="field-group">
                              <view class="field-label">过期时间</view>
                              <wd-datetime-picker
                                v-model="lot.expirationTime"
                                placeholder="过期时间"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                            <view class="field-group">
                              <view class="field-label">报废日期</view>
                              <wd-datetime-picker
                                v-model="lot.discardDate"
                                placeholder="报废日期"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                          </view>

                          <view class="field-row">
                            <view class="field-group">
                              <view class="field-label">上次检修</view>
                              <wd-datetime-picker
                                v-model="lot.overhaulLastDate"
                                placeholder="上次检修"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                            <view class="field-group">
                              <view class="field-label">下次检修</view>
                              <wd-datetime-picker
                                v-model="lot.overhaulNextDate"
                                placeholder="下次检修"
                                type="date"
                                custom-class="date-picker"
                              />
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部操作按钮 -->
        <view class="flex p-40rpx gap-20rpx">
          <wd-button custom-class="flex-1" icon="close" type="error" @click="close">取消</wd-button>
          <wd-button
            custom-class="flex-1"
            type="primary"
            icon="check"
            :loading="submitting"
            @click="handleSubmit"
          >
            {{ isEdit ? '更新' : '创建' }}
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统变量
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(60, 60, 67, 0.36);

:deep(.product-form-popup) {
  border-radius: 32rpx 32rpx 0 0;
  // 确保弹窗在最上层，避免被其他元素遮挡
  :deep(.wd-popup) {
    z-index: 10000;
  }

  // 全局移动端优化
  .form-container {
    box-sizing: border-box;
  }

  // 防止任何元素超出容器宽度
  :deep(.form-container) {
    max-width: 100%;
  }

  .form-container {
    background-color: $background-primary;
    border-radius: 40rpx 40rpx 0 0; // Apple风格的圆角
    height: 92vh; // 增加高度，提供更多空间
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    max-width: 100vw; // 确保不超过视口宽度
    overflow: hidden; // 防止内容溢出
    box-sizing: border-box;
    box-shadow: 0 -8rpx 40rpx rgba(0, 0, 0, 0.1); // Apple风格的阴影

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      border-radius: 40rpx 40rpx 0 0;
      backdrop-filter: blur(8rpx);

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 32rpx;
        background: $background-primary;
        padding: 48rpx 64rpx;
        border-radius: 32rpx;
        box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);

        .loading-text {
          font-size: 30rpx;
          color: $system-gray;
          font-weight: 600;
          letter-spacing: -0.4rpx;
        }
      }
    }

    .form-header {
      padding: 32rpx 40rpx;
      border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
      background: $background-primary;
      flex-shrink: 0; // 防止头部被压缩
      position: relative;

      // Apple风格的顶部指示器
      &::before {
        content: '';
        position: absolute;
        top: 16rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 72rpx;
        height: 8rpx;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 4rpx;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;

        .header-title {
          font-size: 40rpx;
          font-weight: 700; // Apple风格的粗体
          color: $label-primary;
          letter-spacing: -1rpx; // Apple风格的字母间距
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 24rpx;

          :deep(.test-btn) {
            padding: 16rpx 24rpx;
            background-color: rgba(0, 122, 255, 0.08);
            border-radius: 24rpx;
            color: $system-blue;
            font-size: 26rpx;
            font-weight: 600;
            transition: all 0.2s ease;
            border: 2rpx solid rgba(0, 122, 255, 0.15);

            &:active {
              background-color: rgba(0, 122, 255, 0.15);
              transform: scale(0.96);
            }
          }

          .close-btn {
            padding: 16rpx;
            color: $system-gray;
            transition: all 0.2s ease;
            border-radius: 20rpx;

            &:active {
              color: $label-primary;
              background-color: rgba(0, 0, 0, 0.05);
              transform: scale(0.9);
            }
          }
        }
      }
    }

    .form-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden; // 防止水平滚动
      -webkit-overflow-scrolling: touch; // iOS 平滑滚动
      width: 100%;
      box-sizing: border-box;
      background: linear-gradient(180deg, $background-primary 0%, rgba(242, 242, 247, 0.3) 100%);

      .form-section {
        margin-bottom: 48rpx;
        background: $background-primary;
        padding: 40rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

        .section-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 32rpx;
          padding-bottom: 24rpx;
          border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);

          .section-title {
            display: flex;
            align-items: center;
            gap: 20rpx;
            font-size: 36rpx;
            font-weight: 700;
            color: $label-primary;
            letter-spacing: -0.6rpx;
          }

          :deep(.add-btn) {
            display: flex;
            align-items: center;
            gap: 12rpx;
            padding: 16rpx 32rpx;
            border-radius: 32rpx;
            background: linear-gradient(135deg, $system-blue 0%, darken($system-blue, 5%) 100%);
            color: white;
            font-size: 28rpx;
            font-weight: 600;
            transition: all 0.2s ease;
            box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.2);

            &:active {
              background: linear-gradient(
                135deg,
                darken($system-blue, 10%) 0%,
                darken($system-blue, 15%) 100%
              );
              transform: scale(0.96);
              box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
            }
          }
        }

        .form-fields {
          .field-group {
            margin-bottom: 36rpx;

            .field-label {
              display: block;
              font-size: 30rpx;
              font-weight: 600;
              color: $label-primary;
              margin-bottom: 16rpx;
              letter-spacing: -0.4rpx;

              &.required::after {
                content: ' *';
                color: $system-red;
                font-weight: 700;
              }
            }

            .product-number-wrapper {
              display: flex;
              gap: 20rpx;
              align-items: center;

              :deep(.form-input) {
                flex: 1;
              }

              :deep(.copy-btn) {
                flex-shrink: 0;
                padding: 16rpx 28rpx;
                border-radius: 24rpx;
                background: linear-gradient(
                  135deg,
                  rgba(0, 122, 255, 0.08) 0%,
                  rgba(0, 122, 255, 0.12) 100%
                );
                color: $system-blue;
                font-size: 26rpx;
                font-weight: 600;
                transition: all 0.2s ease;
                border: 2rpx solid rgba(0, 122, 255, 0.15);
                box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.1);

                &:active {
                  background: linear-gradient(
                    135deg,
                    rgba(0, 122, 255, 0.15) 0%,
                    rgba(0, 122, 255, 0.2) 100%
                  );
                  transform: scale(0.96);
                }
              }
            }

            :deep(.form-input),
            :deep(.form-select),
            :deep(.form-number) {
              width: 100%;
              border-radius: 28rpx;
              border: 2rpx solid rgba(0, 0, 0, 0.08);
              background-color: $background-primary;
              padding: 28rpx 32rpx;
              font-size: 32rpx;
              font-weight: 500;
              transition: all 0.2s ease;
              box-sizing: border-box;
              min-width: 0; // 防止flex子项收缩问题
              box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.04);

              &:focus {
                border-color: $system-blue;
                box-shadow:
                  0 0 0 6rpx rgba(0, 122, 255, 0.1),
                  0 4rpx 16rpx rgba(0, 122, 255, 0.1);
                outline: none;
              }

              &::placeholder {
                color: rgba(60, 60, 67, 0.3);
                font-weight: 400;
              }
            }

            :deep(.form-switch) {
              margin-top: 12rpx;
            }

            .image-upload {
              display: flex;
              justify-content: center;
            }

            .error-text {
              font-size: 26rpx;
              color: $system-red;
              margin-top: 12rpx;
              font-weight: 500;
            }
          }
        }

        .sku-list {
          .sku-card {
            background: linear-gradient(
              135deg,
              $background-secondary 0%,
              rgba(255, 255, 255, 0.8) 100%
            );
            border-radius: 32rpx;
            padding: 36rpx;
            margin-bottom: 32rpx;
            border: 2rpx solid rgba(0, 0, 0, 0.06);
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
            transition: all 0.2s ease;

            &:hover {
              box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
              transform: translateY(-2rpx);
            }

            .sku-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 32rpx;
              padding-bottom: 24rpx;
              border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);

              .sku-title {
                font-size: 34rpx;
                font-weight: 700;
                color: $label-primary;
                letter-spacing: -0.6rpx;
              }

              :deep(.remove-btn) {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 64rpx;
                height: 64rpx;
                border-radius: 32rpx;
                background: linear-gradient(
                  135deg,
                  rgba(255, 59, 48, 0.1) 0%,
                  rgba(255, 59, 48, 0.15) 100%
                );
                color: $system-red;
                transition: all 0.2s ease;
                border: 2rpx solid rgba(255, 59, 48, 0.2);

                &:active {
                  background: linear-gradient(
                    135deg,
                    rgba(255, 59, 48, 0.2) 0%,
                    rgba(255, 59, 48, 0.25) 100%
                  );
                  transform: scale(0.9);
                }
              }
            }

            .sku-fields {
              .field-row {
                display: block; // 移动端优先使用垂直布局
                margin-bottom: 20rpx;

                .field-group {
                  width: 100%;
                  margin-bottom: 20rpx;

                  .field-label {
                    font-size: 26rpx;
                    margin-bottom: 8rpx;
                    color: $label-secondary;
                  }

                  .sku-image-upload {
                    display: flex;
                    justify-content: flex-start;
                  }

                  :deep(.sku-switch) {
                    margin-top: 4rpx;
                  }

                  :deep(.form-input),
                  :deep(.form-number) {
                    width: 100%;
                    padding: 16rpx 20rpx;
                    font-size: 28rpx;
                    border-radius: 16rpx;
                    box-sizing: border-box;
                  }
                }
              }
            }

            .lots-section {
              margin-top: 32rpx;
              padding-top: 32rpx;
              border-top: 2rpx solid rgba(0, 0, 0, 0.08);

              .lots-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24rpx;

                .lots-title {
                  font-size: 30rpx;
                  font-weight: 700;
                  color: $label-primary;
                  letter-spacing: -0.4rpx;
                }

                :deep(.add-lot-btn) {
                  display: flex;
                  align-items: center;
                  gap: 8rpx;
                  padding: 12rpx 24rpx;
                  border-radius: 24rpx;
                  background: linear-gradient(
                    135deg,
                    rgba(0, 122, 255, 0.08) 0%,
                    rgba(0, 122, 255, 0.12) 100%
                  );
                  color: $system-blue;
                  font-size: 24rpx;
                  font-weight: 600;
                  transition: all 0.2s ease;
                  border: 2rpx solid rgba(0, 122, 255, 0.15);
                  box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.1);

                  &:active {
                    background: linear-gradient(
                      135deg,
                      rgba(0, 122, 255, 0.15) 0%,
                      rgba(0, 122, 255, 0.2) 100%
                    );
                    transform: scale(0.96);
                  }
                }
              }

              .lot-list {
                .lot-card {
                  background: linear-gradient(
                    135deg,
                    $background-primary 0%,
                    rgba(248, 248, 250, 0.8) 100%
                  );
                  border-radius: 24rpx;
                  padding: 28rpx;
                  margin-bottom: 16rpx;
                  border: 2rpx solid rgba(0, 0, 0, 0.06);
                  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
                  transition: all 0.2s ease;

                  &:hover {
                    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06);
                    transform: translateY(-2rpx);
                  }

                  .lot-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 24rpx;
                    padding-bottom: 16rpx;
                    border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);

                    .lot-title {
                      font-size: 28rpx;
                      font-weight: 700;
                      color: $label-primary;
                      letter-spacing: -0.4rpx;
                    }

                    :deep(.remove-lot-btn) {
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      width: 52rpx;
                      height: 52rpx;
                      border-radius: 26rpx;
                      background: linear-gradient(
                        135deg,
                        rgba(255, 59, 48, 0.1) 0%,
                        rgba(255, 59, 48, 0.15) 100%
                      );
                      color: $system-red;
                      transition: all 0.2s ease;
                      border: 2rpx solid rgba(255, 59, 48, 0.2);

                      &:active {
                        background: linear-gradient(
                          135deg,
                          rgba(255, 59, 48, 0.2) 0%,
                          rgba(255, 59, 48, 0.25) 100%
                        );
                        transform: scale(0.9);
                      }
                    }
                  }

                  .lot-fields {
                    .field-row {
                      display: block; // 移动端优先垂直布局
                      margin-bottom: 16rpx;

                      .field-group {
                        width: 100%;
                        margin-bottom: 16rpx;

                        .field-label {
                          font-size: 22rpx;
                          margin-bottom: 6rpx;
                          color: $label-secondary;
                          font-weight: 500;
                        }

                        :deep(.lot-input),
                        :deep(.lot-number),
                        :deep(.date-picker) {
                          width: 100%;
                          border-radius: 20rpx;
                          border: 2rpx solid rgba(0, 0, 0, 0.08);
                          font-size: 26rpx;
                          padding: 16rpx 24rpx;
                          background-color: #fff;
                          transition: all 0.2s ease;
                          box-sizing: border-box;
                          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.02);

                          &:focus {
                            border-color: $system-blue;
                            box-shadow:
                              0 0 0 6rpx rgba(0, 122, 255, 0.1),
                              0 4rpx 12rpx rgba(0, 122, 255, 0.1);
                            outline: none;
                          }

                          &::placeholder {
                            color: rgba(60, 60, 67, 0.3);
                            font-weight: 400;
                          }
                        }
                      }
                    }

                    .date-section {
                      margin-top: 16rpx;
                      padding-top: 16rpx;
                      border-top: 2rpx solid rgba(0, 0, 0, 0.06);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
// Apple风格的动画效果
.form-container {
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.form-section {
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;

  &:nth-child(1) {
    animation-delay: 0.1s;
  }
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(40rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
