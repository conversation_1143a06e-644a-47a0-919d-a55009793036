<script setup lang="ts">
import type { IOrder } from '@/api/interface/rescue/order/order'
import { reviewOrderApi } from '@/api/modules/rescue/order/order'
import ReviewStatusSelect from '@/components/rescue/ReviewStatusSelect.vue'

// Props
interface Props {
  id?: number
  orderInfo?: {
    orderNumber: string
    reason: string
    applyName: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  id: 0,
  orderInfo: null,
})

// Emits
const emit = defineEmits<{
  success: []
}>()

// 计算属性处理 v-model
const isVisible = defineModel<boolean>({
  type: Boolean,
  default: false,
})

// 表单数据
const formData = ref<IOrder.OrderReviewDTO>({
  id: 0,
  status: '',
  reviewOpinion: '',
})

// 提交状态
const submitting = ref(false)

// 表单错误
const errors = ref({
  status: '',
  reviewOpinion: '',
})

// 监听弹窗状态
watch(isVisible, (newVal) => {
  if (newVal && props.id) {
    // 重置表单
    formData.value = {
      id: props.id,
      status: '',
      reviewOpinion: '',
    }
    errors.value = {
      status: '',
      reviewOpinion: '',
    }
  }
})

// 验证表单
function validateForm() {
  let isValid = true
  errors.value = {
    status: '',
    reviewOpinion: '',
  }

  if (!formData.value.status) {
    errors.value.status = '请选择审批状态'
    isValid = false
  }

  return isValid
}

// 处理状态选择
function handleStatusChange(status: string) {
  formData.value.status = status
  errors.value.status = ''
}

// 关闭弹窗
function handleClose() {
  isVisible.value = false
}

// 提交审批
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  try {
    submitting.value = true

    await reviewOrderApi(formData.value)

    uni.showToast({
      title: '审批成功',
      icon: 'success',
    })

    emit('success')
    handleClose()
  } catch (error) {
    console.error('审批失败:', error)
    uni.showToast({
      title: '审批失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}
</script>

<template>
  <view>
    <!-- 外层view组件不能删除，否则微信小程样式会不生效！！！ -->
    <wd-popup
      v-model="isVisible"
      position="bottom"
      custom-style="border-radius: 40rpx 40rpx 0 0;"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="99999"
    >
      <view class="review-dialog">
        <!-- 标题栏 -->
        <view class="dialog-header">
          <view class="dialog-title">审批订单</view>
          <view class="close-btn" @tap="handleClose">
            <wd-icon name="close" size="48rpx" color="#86868B" />
          </view>
        </view>

        <!-- 订单信息 -->
        <view v-if="orderInfo" class="order-info-section">
          <view class="info-item">
            <view class="info-label">订单编号</view>
            <view class="info-value">
              {{ orderInfo.orderNumber }}
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">申请原因</view>
            <view class="info-value">
              {{ orderInfo.reason }}
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">申请人</view>
            <view class="info-value">
              {{ orderInfo.applyName }}
            </view>
          </view>
        </view>

        <!-- 表单内容 -->
        <view class="dialog-content">
          <!-- 审批状态选择 -->
          <view class="form-item">
            <ReviewStatusSelect
              v-model="formData.status"
              :error-message="errors.status"
              :disabled="submitting"
              required
              @update:model-value="handleStatusChange"
            />
          </view>

          <!-- 审批意见 -->
          <view class="form-item">
            <view class="form-label">
              <view class="label-text">审批意见</view>
              <view class="label-optional">（选填）</view>
            </view>
            <view class="textarea-wrapper">
              <textarea
                v-model="formData.reviewOpinion"
                class="review-textarea"
                placeholder="请输入审批意见..."
                :disabled="submitting"
                :maxlength="200"
                auto-height
              />
              <view class="char-count">{{ formData.reviewOpinion.length }}/200</view>
            </view>
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="dialog-footer">
          <wd-button type="info" size="large" plain :disabled="submitting" @tap="handleClose">
            取消
          </wd-button>
          <wd-button type="primary" size="large" :loading="submitting" @tap="handleSubmit">
            {{ submitting ? '提交中...' : '确认审批' }}
          </wd-button>
        </view>

        <!-- 安全区域 -->
        <view class="safe-area-bottom" />
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统颜色
$label-primary: rgba(0, 0, 0, 0.85);
$label-secondary: rgba(0, 0, 0, 0.5);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(0, 0, 0, 0.1);
$system-blue: #007aff;

.review-dialog {
  background: $background-primary;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

// 标题栏
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);

  .dialog-title {
    font-size: 36rpx;
    font-weight: 700;
    color: #1d1d1f;
    letter-spacing: -0.8rpx;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .close-btn {
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 28rpx;
    background: linear-gradient(135deg, #f2f2f7 0%, #e5e5ea 100%);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
      0 2rpx 8rpx rgba(0, 0, 0, 0.08),
      0 0 0 2rpx rgba(0, 0, 0, 0.04);

    wd-icon {
      color: #86868b;
      transition: color 0.2s ease;
    }

    &:active {
      transform: scale(0.92);
      background: linear-gradient(135deg, #e5e5ea 0%, #d1d1d6 100%);
      box-shadow:
        0 1rpx 4rpx rgba(0, 0, 0, 0.12),
        0 0 0 2rpx rgba(0, 0, 0, 0.08);

      wd-icon {
        color: #1d1d1f;
      }
    }

    &:hover {
      box-shadow:
        0 4rpx 12rpx rgba(0, 0, 0, 0.12),
        0 0 0 2rpx rgba(0, 0, 0, 0.06);
    }
  }
}

// 订单信息
.order-info-section {
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f8fa 0%, #f2f2f7 100%);
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.08);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 122, 255, 0.1) 50%,
      transparent 100%
    );
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20rpx;
    padding: 16rpx 20rpx;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(10rpx);
    transition: all 0.2s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
      transform: translateY(-1rpx);
    }

    .info-label {
      font-size: 28rpx;
      color: #8e8e93;
      margin-right: 20rpx;
      min-width: 140rpx;
      flex-shrink: 0;
      font-weight: 600;
      letter-spacing: -0.2rpx;
    }

    .info-value {
      font-size: 28rpx;
      color: #1d1d1f;
      flex: 1;
      line-height: 1.5;
      font-weight: 500;
      letter-spacing: -0.2rpx;
    }
  }
}

// 表单内容
.dialog-content {
  padding: 32rpx;
  flex: 1;
  overflow-y: auto;

  .form-item {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 4rpx;

    .label-text {
      font-size: 32rpx;
      font-weight: 700;
      color: #1d1d1f;
      letter-spacing: -0.6rpx;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .label-optional {
      font-size: 26rpx;
      color: #8e8e93;
      margin-left: 12rpx;
      font-weight: 500;
      background: rgba(142, 142, 147, 0.1);
      padding: 4rpx 8rpx;
      border-radius: 6rpx;
    }
  }

  .textarea-wrapper {
    position: relative;
    background: linear-gradient(135deg, #f2f2f7 0%, #e5e5ea 100%);
    border-radius: 16rpx;
    padding: 24rpx;
    border: 2rpx solid rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
      0 2rpx 8rpx rgba(0, 0, 0, 0.04),
      inset 0 1rpx 2rpx rgba(255, 255, 255, 0.8);

    &:focus-within {
      border-color: #007aff;
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(0, 122, 255, 0.02) 100%);
      box-shadow:
        0 4rpx 16rpx rgba(0, 122, 255, 0.15),
        0 0 0 2rpx rgba(0, 122, 255, 0.1),
        inset 0 1rpx 2rpx rgba(255, 255, 255, 0.9);
      transform: translateY(-1rpx);
    }

    .review-textarea {
      width: 100%;
      min-height: 140rpx;
      font-size: 30rpx;
      color: #1d1d1f;
      line-height: 1.6;
      background: transparent;
      border: none;
      outline: none;
      resize: none;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      letter-spacing: -0.2rpx;

      &::placeholder {
        color: #8e8e93;
        font-weight: 400;
      }

      &:disabled {
        opacity: 0.5;
        color: #8e8e93;
      }
    }

    .char-count {
      position: absolute;
      right: 24rpx;
      bottom: 24rpx;
      font-size: 24rpx;
      color: #8e8e93;
      font-weight: 500;
      background: rgba(255, 255, 255, 0.8);
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
      backdrop-filter: blur(10rpx);
    }
  }
}

// 底部按钮
.dialog-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 2rpx solid $separator;
  background: $background-primary;

  wd-button {
    flex: 1;
    height: 88rpx;
    border-radius: 16rpx;
    font-weight: 600;
    font-size: 32rpx;
    letter-spacing: -0.6rpx;
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;

    // 触觉反馈
    &:active {
      transform: scale(0.96);
      transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    // 取消按钮样式 - Apple Gray
    &[type='info'] {
      background: linear-gradient(135deg, #f2f2f7 0%, #e5e5ea 100%);
      border: 2rpx solid rgba(0, 0, 0, 0.08);
      color: #1d1d1f;
      box-shadow:
        0 2rpx 8rpx rgba(0, 0, 0, 0.08),
        0 0 0 2rpx rgba(0, 0, 0, 0.04);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.6) 0%,
          rgba(255, 255, 255, 0.3) 100%
        );
        border-radius: 16rpx;
        pointer-events: none;
      }

      &:active {
        background: linear-gradient(135deg, #e5e5ea 0%, #d1d1d6 100%);
        box-shadow:
          0 1rpx 4rpx rgba(0, 0, 0, 0.12),
          0 0 0 2rpx rgba(0, 0, 0, 0.08);
        color: #000000;
      }

      &:hover {
        box-shadow:
          0 4rpx 12rpx rgba(0, 0, 0, 0.12),
          0 0 0 2rpx rgba(0, 0, 0, 0.06);
      }

      &:disabled {
        background: linear-gradient(135deg, #f2f2f7 0%, #e5e5ea 100%);
        color: #8e8e93;
        opacity: 0.6;
        box-shadow:
          0 1rpx 4rpx rgba(0, 0, 0, 0.04),
          0 0 0 2rpx rgba(0, 0, 0, 0.02);
      }
    }

    // 确认按钮样式 - Apple Blue
    &[type='primary'] {
      background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
      border: none;
      color: #ffffff;
      box-shadow:
        0 2rpx 8rpx rgba(0, 122, 255, 0.25),
        0 0 0 2rpx rgba(0, 122, 255, 0.1);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        border-radius: 16rpx;
        pointer-events: none;
      }

      &:active {
        background: linear-gradient(135deg, #0056cc 0%, #007aff 100%);
        box-shadow:
          0 1rpx 4rpx rgba(0, 122, 255, 0.4),
          0 0 0 2rpx rgba(0, 122, 255, 0.2);
      }

      &:hover {
        box-shadow:
          0 4rpx 16rpx rgba(0, 122, 255, 0.35),
          0 0 0 2rpx rgba(0, 122, 255, 0.15);
      }

      &:disabled {
        background: linear-gradient(135deg, #8e8e93 0%, #aeaeb2 100%);
        color: #ffffff;
        opacity: 0.6;
        box-shadow:
          0 1rpx 4rpx rgba(142, 142, 147, 0.3),
          0 0 0 2rpx rgba(142, 142, 147, 0.1);
      }

      // 加载状态样式
      &.wd-loading {
        background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
        color: transparent;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 32rpx;
          height: 32rpx;
          margin: -16rpx 0 0 -16rpx;
          border: 3rpx solid rgba(255, 255, 255, 0.3);
          border-top: 3rpx solid #ffffff;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 安全区域
.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
