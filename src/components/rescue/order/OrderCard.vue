<script setup lang="ts">
import type { IOrder } from '@/api/interface/rescue/order/order'

// Props
interface Props {
  order: IOrder.OrderVO
  showApplicant?: boolean // 是否显示申请人信息
  showReviewer?: boolean // 是否显示审批人信息
  showPrice?: boolean // 是否显示价格
  loading?: boolean // 是否显示加载状态
  showReviewButton?: boolean // 是否显示审批按钮
}

const props = withDefaults(defineProps<Props>(), {
  showApplicant: false,
  showReviewer: false,
  showPrice: false,
  loading: false,
  showReviewButton: false,
})

// Emits
const emit = defineEmits<{
  click: [order: IOrder.OrderVO]
  copyOrderNumber: [orderNumber: string]
  review: [order: IOrder.OrderVO]
  applyInbound: [order: IOrder.OrderVO]
}>()

// 格式化时间
function formatTime(timeStr: string) {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 格式化金额
function formatPrice(price: number | undefined) {
  if (price === undefined || price === null || Number.isNaN(price)) {
    return '¥0.00'
  }
  return `¥${Number(price).toFixed(2)}`
}

// 复制订单编号
function copyOrderNumber(e: Event) {
  e.stopPropagation()
  uni.setClipboardData({
    data: props.order.orderNumber,
    success: () => {
      uni.showToast({
        title: '订单编号已复制',
        icon: 'success',
        duration: 2000,
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000,
      })
    },
  })
  emit('copyOrderNumber', props.order.orderNumber)
}

// 点击卡片
function handleClick() {
  if (!props.loading) {
    emit('click', props.order)
  }
}

// 处理审批按钮点击
function handleReview(e: Event) {
  e.stopPropagation()
  emit('review', props.order)
}

// 判断是否显示审批按钮
const canReview = computed(() => {
  // 定义不需要显示审批按钮的状态
  const nonReviewableStatuses = ['OutboundComplete', 'InboundComplete', 'Terminate']

  return props.showReviewButton && !nonReviewableStatuses.includes(props.order.status)
})

// 判断是否显示申请入库按钮
const canApplyInbound = computed(() => {
  return props.order.status === 'OutboundComplete'
})

// 处理申请入库按钮点击
function handleApplyInbound(e: Event) {
  e.stopPropagation()
  emit('applyInbound', props.order)
}
</script>

<template>
  <view class="order-card" :class="{ loading }" @tap="handleClick">
    <!-- 加载遮罩 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-spinner">
        <view class="spinner-dot" />
        <view class="spinner-dot" />
        <view class="spinner-dot" />
      </view>
    </view>

    <view class="card-content">
      <!-- 头部信息 -->
      <view class="card-header">
        <view class="order-info">
          <view class="order-number-wrapper" @tap="copyOrderNumber">
            <view class="order-number">
              {{ order.orderNumber }}
            </view>
            <wd-icon name="copy" size="28rpx" class="copy-icon" />
          </view>
          <view class="work-number">
            {{ order.workNumber }}
          </view>
        </view>
        <OrderReviewStatusShow :status="order.status" variant="ghost" size="small" />
      </view>

      <!-- 主体信息 -->
      <view class="card-body">
        <view class="apply-info">
          <view class="reason">
            {{ order.reason }}
          </view>
          <view v-if="order.remark" class="remark">备注: {{ order.remark }}</view>
          <view class="meta-info">
            <view class="apply-time">申请时间: {{ formatTime(order.createTime) }}</view>
            <view v-if="showApplicant && order.applyName" class="apply-name">
              申请人: {{ order.applyName }}
            </view>
          </view>
        </view>

        <view class="amount-info">
          <view class="total-amount">共{{ order.totalAmount }}件</view>
          <view v-if="showPrice" class="total-price">
            {{ formatPrice(order.totalPrice) }}
          </view>
        </view>
      </view>

      <!-- 审批信息 -->
      <view v-if="showReviewer && (order.reviewName || order.reviewOpinion)" class="card-review">
        <view class="review-header">
          <view class="review-info">
            <view v-if="order.reviewName" class="reviewer">审批人: {{ order.reviewName }}</view>
            <view v-if="order.reviewPhone" class="reviewer-phone">
              {{ order.reviewPhone }}
            </view>
          </view>
        </view>
        <view v-if="order.reviewOpinion" class="review-opinion-wrapper">
          <view class="review-opinion">
            {{ order.reviewOpinion }}
          </view>
        </view>
      </view>

      <!-- 插槽区域 -->
      <slot name="extra" />

      <!-- 底部区域 -->
      <view v-if="canReview || canApplyInbound || $slots.footer" class="card-footer">
        <!-- 审批按钮 -->
        <view v-if="canReview" class="action-buttons">
          <wd-button
            type="primary"
            icon="check-circle"
            size="small"
            custom-class="review-button"
            @tap="handleReview"
          >
            立即审批
          </wd-button>
        </view>

        <!-- 申请入库按钮 -->
        <view v-if="canApplyInbound" class="action-buttons">
          <wd-button
            type="success"
            size="small"
            icon="arrow-down-circle"
            custom-class="inbound-button"
            @tap="handleApplyInbound"
          >
            申请入库
          </wd-button>
        </view>

        <!-- 默认指示器或插槽内容 -->
        <slot v-else-if="$slots.footer" name="footer" />

        <!-- 默认指示器 -->
        <view v-else class="access-indicator">
          <wd-icon name="arrow-right" size="32rpx" color="#007AFF" />
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统颜色
$system-blue: #007aff;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: rgba(0, 0, 0, 0.85);
$label-secondary: rgba(0, 0, 0, 0.5);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;

// 分割线
$separator: rgba(0, 0, 0, 0.1);

.order-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 4rpx 20rpx rgba(0, 0, 0, 0.08),
    0 0 0 2rpx rgba(0, 0, 0, 0.04);
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);

  &:active:not(.loading) {
    transform: scale(0.98) translateY(2rpx);
    box-shadow:
      0 2rpx 12rpx rgba(0, 0, 0, 0.12),
      0 0 0 2rpx rgba(0, 0, 0, 0.08);
  }

  &:hover:not(.loading) {
    transform: translateY(-2rpx);
    box-shadow:
      0 8rpx 32rpx rgba(0, 0, 0, 0.12),
      0 0 0 2rpx rgba(0, 0, 0, 0.06);
  }

  &.loading {
    pointer-events: none;
  }

  // 加载遮罩
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .loading-spinner {
      display: flex;
      gap: 8rpx;

      .spinner-dot {
        width: 8rpx;
        height: 8rpx;
        border-radius: 50%;
        background: $system-blue;
        animation: loading-bounce 1.4s infinite ease-in-out both;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }
      }
    }
  }

  .card-content {
    position: relative;
    padding: 24rpx;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;

    .order-info {
      flex: 1;

      .order-number-wrapper {
        display: inline-flex;
        align-items: center;
        gap: 8rpx;
        margin-bottom: 8rpx;
        padding: 8rpx 12rpx;
        margin-left: -12rpx;
        border-radius: 8rpx;
        transition: background 0.15s ease;

        &:active {
          background: $system-gray6;
        }

        .order-number {
          font-size: 34rpx;
          font-weight: 700;
          color: #1d1d1f;
          letter-spacing: -0.8rpx;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .copy-icon {
          color: $system-blue;
          opacity: 0.8;
        }
      }

      .work-number {
        font-size: 24rpx;
        color: $label-secondary;
        font-weight: 400;
      }
    }
  }

  .card-body {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16rpx;

    .apply-info {
      flex: 1;
      margin-right: 20rpx;

      .reason {
        display: block;
        font-size: 30rpx;
        color: #1d1d1f;
        margin-bottom: 12rpx;
        line-height: 1.6;
        font-weight: 500;
        letter-spacing: -0.2rpx;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .remark {
        display: block;
        font-size: 24rpx;
        color: $label-secondary;
        margin-bottom: 8rpx;
        line-height: 1.4;
      }

      .meta-info {
        display: flex;
        flex-direction: column;
        gap: 4rpx;

        .apply-time,
        .apply-name {
          font-size: 22rpx;
          color: $label-secondary;
          font-weight: 400;
        }
      }
    }

    .amount-info {
      text-align: right;
      flex-shrink: 0;

      .total-amount {
        display: block;
        font-size: 26rpx;
        color: $label-secondary;
        margin-bottom: 4rpx;
        font-weight: 400;
      }

      .total-price {
        font-size: 34rpx;
        font-weight: 600;
        color: $system-blue;
        letter-spacing: -0.6rpx;
      }
    }
  }

  .card-review {
    margin-top: 16rpx;
    padding-top: 16rpx;
    border-top: 2rpx solid $separator;

    .review-header {
      margin-bottom: 10rpx;
    }

    .review-info {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .reviewer {
        font-size: 24rpx;
        color: $system-blue;
        font-weight: 500;
      }

      .reviewer-phone {
        font-size: 22rpx;
        color: $label-secondary;
      }
    }

    .review-opinion-wrapper {
      .review-opinion {
        display: block;
        font-size: 24rpx;
        color: $label-primary;
        line-height: 1.5;
        background: $system-gray6;
        padding: 12rpx 16rpx;
        border-radius: 8rpx;
        border-left: 6rpx solid $system-blue;
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 16rpx;

    .action-buttons {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      gap: 12rpx;

      wd-button {
        min-width: 160rpx;
        height: 72rpx;
        border-radius: 12rpx;
        font-weight: 600;
        font-size: 28rpx;
        letter-spacing: -0.6rpx;
        transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        padding: 0 20rpx;
        position: relative;
        overflow: hidden;

        // 触觉反馈
        &:active {
          transform: scale(0.96);
          transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        // 图标样式
        wd-icon {
          font-size: 32rpx;
          transition: transform 0.2s ease;
        }

        &:active wd-icon {
          transform: scale(0.9);
        }

        // 审批按钮样式 - Apple Blue
        .review-button {
          background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
          border: none;
          color: #ffffff;
          box-shadow:
            0 2rpx 8rpx rgba(0, 122, 255, 0.25),
            0 0 0 2rpx rgba(0, 122, 255, 0.1);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.1) 0%,
              rgba(255, 255, 255, 0.05) 100%
            );
            border-radius: 12rpx;
            pointer-events: none;
          }

          &:active {
            background: linear-gradient(135deg, #0056cc 0%, #007aff 100%);
            box-shadow:
              0 1rpx 4rpx rgba(0, 122, 255, 0.4),
              0 0 0 2rpx rgba(0, 122, 255, 0.2);
          }

          &:hover {
            box-shadow:
              0 4rpx 16rpx rgba(0, 122, 255, 0.35),
              0 0 0 2rpx rgba(0, 122, 255, 0.15);
          }
        }

        // 申请入库按钮样式 - Apple Green
        .inbound-button {
          background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
          border: none;
          color: #ffffff;
          box-shadow:
            0 2rpx 8rpx rgba(52, 199, 89, 0.25),
            0 0 0 2rpx rgba(52, 199, 89, 0.1);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.1) 0%,
              rgba(255, 255, 255, 0.05) 100%
            );
            border-radius: 12rpx;
            pointer-events: none;
          }

          &:active {
            background: linear-gradient(135deg, #248a3d 0%, #34c759 100%);
            box-shadow:
              0 1rpx 4rpx rgba(52, 199, 89, 0.4),
              0 0 0 2rpx rgba(52, 199, 89, 0.2);
          }

          &:hover {
            box-shadow:
              0 4rpx 16rpx rgba(52, 199, 89, 0.35),
              0 0 0 2rpx rgba(52, 199, 89, 0.15);
          }
        }
      }
    }

    .access-indicator {
      width: 48rpx;
      height: 48rpx;
      background: linear-gradient(135deg, #f2f2f7 0%, #e5e5ea 100%);
      border-radius: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow:
        0 2rpx 8rpx rgba(0, 0, 0, 0.08),
        0 0 0 2rpx rgba(0, 0, 0, 0.04);

      wd-icon {
        color: #007aff;
        transition: transform 0.2s ease;
      }

      &:active {
        transform: scale(0.92);
        background: linear-gradient(135deg, #e5e5ea 0%, #d1d1d6 100%);
        box-shadow:
          0 1rpx 4rpx rgba(0, 0, 0, 0.12),
          0 0 0 2rpx rgba(0, 0, 0, 0.08);
      }

      &:hover {
        box-shadow:
          0 4rpx 12rpx rgba(0, 0, 0, 0.12),
          0 0 0 2rpx rgba(0, 0, 0, 0.06);
      }
    }
  }
}

// 加载动画
@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 进入动画
@keyframes card-enter {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
