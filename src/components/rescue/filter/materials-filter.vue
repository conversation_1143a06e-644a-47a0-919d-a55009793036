<script setup lang="ts">
import rfdc from 'rfdc'

// 定义组件名称
defineOptions({
  name: 'MaterialsFilter',
  options: {
    styleIsolation: 'shared',
  },
})
const props = withDefaults(defineProps<Props>(), {
  showWarehouse: true,
  showTags: true,
  showSearch: true,
  defaultExpanded: true,
  zIndex: 9,
})
const emit = defineEmits<{
  (e: 'search'): void
  (e: 'reset'): void
  (e: 'warehouseChange', value: number | number[]): void
  (e: 'tagChange', value: number | number[]): void
}>()
const cloneDeep = rfdc()
// 组件会通过全局注册或者自动导入的方式使用，无需显式导入

interface FilterParams {
  warehouseId?: number
  tagIds?: number[]
  searchType?: string
  searchText?: string
  productName?: string
  productNumber?: string
  [key: string]: any
}

interface Props {
  /** 是否显示仓库筛选 */
  showWarehouse?: boolean
  /** 是否显示标签筛选 */
  showTags?: boolean
  /** 是否显示搜索功能 */
  showSearch?: boolean
  /** 默认是否展开 */
  defaultExpanded?: boolean
  /** 弹窗层级 */
  zIndex?: number
}

const modelValue = defineModel<FilterParams>({
  default: () => ({
    warehouseId: undefined,
    tagIds: [],
    searchType: 'name',
    searchText: '',
    productName: '',
    productNumber: '',
  }),
})

// 状态管理
const showFilterPanel = ref(props.defaultExpanded)
const showSearchTypePicker = ref(false)

// 搜索类型配置
const searchTypes = [
  {
    value: 'name',
    label: '名称',
    description: '按物资名称搜索',
    field: 'productName',
  },
  {
    value: 'number',
    label: '编号',
    description: '按物资编号搜索',
    field: 'productNumber',
  },
]

// 计算属性
const currentSearchType = computed(() => {
  return searchTypes.find((type) => type.value === modelValue.value.searchType) || searchTypes[0]
})

const hasActiveFilters = computed(() => {
  const filters = modelValue.value
  return !!(
    filters.warehouseId ||
    (filters.tagIds && filters.tagIds.length > 0) ||
    filters.searchText ||
    filters.productName ||
    filters.productNumber
  )
})

// 事件处理
function toggleFilterPanel() {
  showFilterPanel.value = !showFilterPanel.value
}

function handleWarehouseChange(value: number | number[]) {
  modelValue.value.warehouseId = Array.isArray(value) ? value[0] : value
  emit('warehouseChange', value)
  emit('search')
}

function handleTagChange(value: number | number[]) {
  modelValue.value.tagIds = Array.isArray(value) ? value : [value]
  emit('tagChange', value)
  emit('search')
}

function handleSearchTypeChange(type: string) {
  const _modelValue = cloneDeep(modelValue.value)
  _modelValue.searchType = type
  showSearchTypePicker.value = false

  // 清空之前的搜索内容
  _modelValue.productName = ''
  _modelValue.productNumber = ''
  // 防止多次自动 emit
  modelValue.value = _modelValue
  // 如果有搜索文本，重新处理
  if (_modelValue.searchText) {
    handleSearchInput()
  }
}

function handleSearchInput() {
  const _modelValue = cloneDeep(modelValue.value)
  const text = _modelValue.searchText?.trim() || ''
  const searchType = currentSearchType.value

  // 清空所有搜索字段
  _modelValue.productName = ''
  _modelValue.productNumber = ''

  // 根据搜索类型设置对应字段
  if (text && searchType.field) {
    _modelValue[searchType.field] = text
  }
  modelValue.value = _modelValue
}

function handleSearch() {
  handleSearchInput()
  emit('search')
}

function clearSearch() {
  const _modelValue = cloneDeep(modelValue.value)
  _modelValue.searchText = ''
  _modelValue.productName = ''
  _modelValue.productNumber = ''
  modelValue.value = _modelValue
  emit('search')
}

function resetFilters() {
  modelValue.value = {
    warehouseId: undefined,
    tagIds: [],
    searchType: 'name',
    searchText: '',
    productName: '',
    productNumber: '',
  }
  emit('reset')
  emit('search')
}

// 暴露方法给父组件
defineExpose({
  resetFilters,
  getFilters: () => cloneDeep(modelValue.value),
  setFilters: (filters: FilterParams) => {
    modelValue.value = { ...modelValue.value, ...filters }
  },
})
</script>

<template>
  <view class="materials-filter">
    <!-- 筛选面板头部 -->
    <view
      class="filter-header"
      :style="showFilterPanel ? 'border-bottom: 2rpx solid rgba(0, 122, 255, 0.1)' : ''"
      @click="toggleFilterPanel"
    >
      <view class="filter-header-left">
        <wd-icon name="filter" size="32rpx" color="#007AFF" />
        <view class="filter-title">筛选条件</view>
        <view v-if="hasActiveFilters" class="filter-active-dot" />
      </view>
      <view class="filter-header-right">
        <wd-button type="text" size="small" custom-class="reset-button" @click.stop="resetFilters">
          重置
        </wd-button>
        <wd-icon
          :name="showFilterPanel ? 'arrow-up' : 'arrow-down'"
          size="32rpx"
          color="#8E8E93"
          class="collapse-icon"
        />
      </view>
    </view>

    <!-- 筛选面板内容 -->
    <view v-show="showFilterPanel" class="filter-content">
      <!-- 仓库筛选 -->
      <view v-if="showWarehouse" class="filter-row">
        <view class="filter-item">
          <WarehouseSelect
            :model-value="modelValue.warehouseId"
            placeholder="选择仓库"
            @update:model-value="handleWarehouseChange"
          />
        </view>
      </view>

      <!-- 标签筛选 -->
      <view v-if="showTags" class="filter-row">
        <view class="filter-item">
          <TagSelect
            v-model="modelValue.tagIds"
            :multiple="true"
            placeholder="选择标签"
            @update:model-value="handleTagChange"
          />
        </view>
      </view>

      <!-- 搜索筛选 -->
      <view v-if="showSearch" class="filter-row">
        <view class="filter-item">
          <view class="search-input-wrapper">
            <!-- 融合式搜索输入框 -->
            <view class="integrated-search-container">
              <!-- 搜索类型选择按钮 -->
              <view class="search-type-button" @click="showSearchTypePicker = true">
                <view class="search-type-label">
                  {{ currentSearchType.label }}
                </view>
                <wd-icon name="arrow-down" size="20rpx" color="#8E8E93" />
              </view>

              <!-- 分割线 -->
              <view class="search-divider" />

              <!-- 搜索输入框区域 -->
              <view class="search-input-section">
                <wd-icon name="search" size="32rpx" color="#8E8E93" class="search-icon" />
                <input
                  v-model="modelValue.searchText"
                  :placeholder="`输入${currentSearchType.label}`"
                  class="search-input"
                  @input="handleSearchInput"
                  @keydown.enter="handleSearch"
                />

                <!-- 内置搜索按钮 -->
                <view class="inline-search-button" @click="handleSearch">
                  <wd-icon name="search" size="28rpx" color="#007AFF" />
                </view>

                <view v-if="modelValue.searchText" class="clear-button" @click="clearSearch">
                  <wd-icon name="close-circle-fill" size="32rpx" color="#C7C7CC" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 自定义筛选项 -->
      <slot name="custom-filters" :filters="modelValue" />
    </view>

    <!-- 搜索类型选择弹窗 - 不能移到外部否则样式消失！！！ -->
    <wd-popup
      v-model="showSearchTypePicker"
      position="bottom"
      :close-on-click-modal="true"
      :safe-area-inset-bottom="true"
      :lock-scroll="true"
      :z-index="zIndex"
      :root-portal="true"
      custom-class="search-type-popup"
    >
      <view class="popup-header">
        <view class="popup-title">选择搜索类型</view>
        <wd-button type="text" size="small" @click="showSearchTypePicker = false">完成</wd-button>
      </view>
      <view class="search-type-list">
        <view
          v-for="type in searchTypes"
          :key="type.value"
          class="search-type-item"
          :class="{ active: modelValue.searchType === type.value }"
          @click="handleSearchTypeChange(type.value)"
        >
          <view class="type-info">
            <view class="type-label">
              {{ type.label }}
            </view>
            <view class="type-desc">
              {{ type.description }}
            </view>
          </view>
          <view v-if="modelValue.searchType === type.value" class="check-mark">✓</view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统变量
$system-blue: #007aff;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(60, 60, 67, 0.36);

.materials-filter {
  background: $background-primary;
  border-radius: 32rpx;
  // margin-bottom: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  /* 移除可能导致层叠上下文限制的属性 */

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 40rpx;
    background: rgba(0, 122, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 122, 255, 0.1);
    }

    .filter-header-left {
      display: flex;
      align-items: center;
      gap: 20rpx;
      position: relative;

      .filter-title {
        font-size: 34rpx;
        font-weight: 600;
        color: $label-primary;
        letter-spacing: -0.4rpx;
      }

      .filter-active-dot {
        width: 16rpx;
        height: 16rpx;
        background: linear-gradient(45deg, $system-blue, #34c759);
        border-radius: 50%;
        margin-left: 16rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);
        animation: pulse 2s infinite;

        @keyframes pulse {
          0%,
          100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.8;
          }
        }
      }
    }

    .filter-header-right {
      display: flex;
      align-items: center;
      gap: 24rpx;

      :deep(.reset-button) {
        padding: 8rpx 24rpx;
        background: linear-gradient(135deg, rgba(255, 59, 48, 0.1) 0%, rgba(255, 149, 0, 0.1) 100%);
        border: 2rpx solid rgba(255, 59, 48, 0.2);
        border-radius: 28rpx;
        color: $system-red;
        font-size: 26rpx;
        font-weight: 500;
        transition: all 0.2s ease;

        &:active {
          background: linear-gradient(
            135deg,
            rgba(255, 59, 48, 0.2) 0%,
            rgba(255, 149, 0, 0.2) 100%
          );
          transform: scale(0.95);
        }
      }

      .collapse-icon {
        transition: all 0.3s ease;
      }
    }
  }

  .filter-content {
    padding: 40rpx;
    animation: filterExpand 0.3s ease;

    @keyframes filterExpand {
      from {
        opacity: 0;
        max-height: 0;
      }
      to {
        opacity: 1;
        max-height: 500px;
      }
    }
  }

  .filter-row {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .filter-item {
      .filter-label {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: $label-primary;
        margin-bottom: 16rpx;
      }

      .search-input-wrapper {
        .integrated-search-container {
          display: flex;
          align-items: stretch;
          background: $background-primary;
          border: 4rpx solid rgba(0, 122, 255, 0.1);
          border-radius: 32rpx;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);

          &:hover {
            border-color: rgba(0, 122, 255, 0.2);
            box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
          }

          &:focus-within {
            border-color: $system-blue;
            box-shadow: 0 0 0 8rpx rgba(0, 122, 255, 0.1);
          }

          .search-type-button {
            display: flex;
            align-items: center;
            gap: 12rpx;
            padding: 28rpx 32rpx;
            background: linear-gradient(
              135deg,
              rgba(0, 122, 255, 0.05) 0%,
              rgba(52, 199, 89, 0.03) 100%
            );
            border-right: 2rpx solid rgba(0, 122, 255, 0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 140rpx;
            justify-content: center;

            &:active {
              background: linear-gradient(
                135deg,
                rgba(0, 122, 255, 0.1) 0%,
                rgba(52, 199, 89, 0.06) 100%
              );
              transform: scale(0.98);
            }

            .search-type-label {
              font-size: 26rpx;
              font-weight: 600;
              color: $system-blue;
              white-space: nowrap;
              letter-spacing: -0.2rpx;
            }
          }

          .search-divider {
            width: 2rpx;
            background: linear-gradient(
              180deg,
              transparent 0%,
              rgba(0, 122, 255, 0.2) 50%,
              transparent 100%
            );
            margin: 16rpx 0;
          }

          .search-input-section {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 32rpx;
            gap: 24rpx;
            position: relative;

            .search-icon {
              flex-shrink: 0;
              opacity: 0.6;
            }

            .search-input {
              flex: 1;
              border: none;
              outline: none;
              font-size: 32rpx;
              color: $label-primary;
              background: transparent;
              padding: 28rpx 0;
              font-weight: 400;
              letter-spacing: -0.2rpx;

              &::placeholder {
                color: rgba(142, 142, 147, 0.8);
                font-weight: 400;
              }

              &:focus {
                &::placeholder {
                  opacity: 0.5;
                }
              }
            }

            .inline-search-button {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 64rpx;
              height: 64rpx;
              background: linear-gradient(
                135deg,
                rgba(0, 122, 255, 0.1) 0%,
                rgba(52, 199, 89, 0.08) 100%
              );
              border-radius: 32rpx;
              cursor: pointer;
              transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              flex-shrink: 0;
              border: 2rpx solid rgba(0, 122, 255, 0.2);

              &:hover {
                background: linear-gradient(
                  135deg,
                  rgba(0, 122, 255, 0.15) 0%,
                  rgba(52, 199, 89, 0.12) 100%
                );
                transform: scale(1.05);
              }

              &:active {
                transform: scale(0.95);
                background: linear-gradient(
                  135deg,
                  rgba(0, 122, 255, 0.2) 0%,
                  rgba(52, 199, 89, 0.15) 100%
                );
              }
            }

            .clear-button {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 48rpx;
              height: 48rpx;
              border-radius: 24rpx;
              cursor: pointer;
              transition: all 0.2s ease;
              flex-shrink: 0;

              &:active {
                transform: scale(0.9);
                background: rgba(199, 199, 204, 0.2);
              }
            }
          }
        }
      }
    }
  }
}

// 搜索类型选择弹窗
:deep(.search-type-popup) {
  border-radius: 32rpx 32rpx 0 0;
  // 确保弹窗在最上层
  :deep(.wd-popup) {
    z-index: 9999 !important;
  }

  :deep(.wd-popup__mask) {
    z-index: 9998 !important;
  }

  :deep(.wd-popup__content) {
    z-index: 9999 !important;
  }

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 48rpx;
    border-bottom: 2rpx solid rgba(0, 122, 255, 0.1);
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(52, 199, 89, 0.03) 100%);
    backdrop-filter: blur(40rpx);

    .popup-title {
      font-size: 34rpx;
      font-weight: 600;
      color: $label-primary;
      letter-spacing: -0.6rpx;
    }
  }

  .search-type-list {
    background: $background-primary;
    max-height: 40vh;
    overflow-y: auto;
    padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);

    .search-type-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 36rpx 48rpx;
      border-bottom: 2rpx solid rgba(0, 122, 255, 0.08);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;

      &:active {
        background: rgba(0, 122, 255, 0.1);
        transform: scale(0.98);
      }

      &.active {
        background: linear-gradient(
          135deg,
          rgba(0, 122, 255, 0.1) 0%,
          rgba(52, 199, 89, 0.08) 100%
        );
        border-left: 8rpx solid $system-blue;
        padding-left: 40rpx;
      }

      &:last-child {
        border-bottom: none;
      }

      .type-info {
        flex: 1;

        .type-label {
          display: block;
          font-size: 32rpx;
          color: $label-primary;
          font-weight: 600;
          margin-bottom: 8rpx;
          letter-spacing: -0.4rpx;
        }

        .type-desc {
          font-size: 26rpx;
          color: $label-secondary;
          font-weight: 400;
          opacity: 0.8;
        }
      }

      .check-mark {
        font-size: 32rpx;
        color: $system-blue;
        font-weight: bold;
      }
    }
  }
}
</style>
