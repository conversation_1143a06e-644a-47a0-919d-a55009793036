<script setup lang="ts">
interface FilterParams {
  name?: string
  address?: string
  enable?: boolean
  common?: boolean
  page?: number
  limit?: number
  [key: string]: any
}

interface Props {
  /** 默认是否展开 */
  defaultExpanded?: boolean
}

// 定义组件名称
defineOptions({
  name: 'WarehouseFilter',
})

const modelValue = defineModel<FilterParams>({
  default: () => ({
    name: '',
    address: '',
    enable: null,
    common: null,
    page: 1,
    limit: 10,
  }),
})

const props = withDefaults(defineProps<Props>(), {
  defaultExpanded: false,
})

const emit = defineEmits<{
  (e: 'search'): void
  (e: 'reset'): void
}>()

// 状态管理
const showFilterPanel = ref(props.defaultExpanded)

// 下拉选项数据
const enableOptions = [
  { label: '启用', value: true },
  { label: '禁用', value: false },
]

const commonOptions = [
  { label: '公共仓库', value: true },
  { label: '私有仓库', value: false },
]

// 计算属性
const hasActiveFilters = computed(() => {
  const filters = modelValue.value
  return !!(filters.name || filters.address || filters.enable !== null || filters.common !== null)
})

// 事件处理
function toggleFilterPanel() {
  showFilterPanel.value = !showFilterPanel.value
}

function handleSearch() {
  console.log('触发搜索，当前筛选条件:', modelValue.value)
  emit('search')
}

function handleEnableChange(value: any) {
  console.log('启用状态变化，原始值:', value) // 调试日志
  console.log('启用状态变化，值类型:', typeof value) // 调试日志
  console.log('启用状态变化，值内容:', JSON.stringify(value)) // 调试日志

  // 处理 wd-picker 返回的值
  let enableValue: boolean | null = null

  // wd-picker 通常返回数组，第一个元素是选中的值
  if (Array.isArray(value) && value.length > 0) {
    enableValue = value[0]
  } else if (value && typeof value === 'object' && value.value !== undefined) {
    enableValue = value.value
  } else if (typeof value === 'boolean') {
    enableValue = value
  } else if (typeof value === 'string') {
    if (value === 'true') enableValue = true
    else if (value === 'false') enableValue = false
  }

  modelValue.value.enable = enableValue
  console.log('启用状态已更新，等待用户手动搜索') // 调试日志
}

function handleCommonChange(value: any) {
  console.log('仓库类型变化，原始值:', value) // 调试日志
  console.log('仓库类型变化，值类型:', typeof value) // 调试日志
  console.log('仓库类型变化，值内容:', JSON.stringify(value)) // 调试日志

  // 处理 wd-picker 返回的值
  let commonValue: boolean | null = null

  // wd-picker 通常返回数组，第一个元素是选中的值
  if (Array.isArray(value) && value.length > 0) {
    commonValue = value[0]
  } else if (value && typeof value === 'object' && value.value !== undefined) {
    commonValue = value.value
  } else if (typeof value === 'boolean') {
    commonValue = value
  } else if (typeof value === 'string') {
    if (value === 'true') commonValue = true
    else if (value === 'false') commonValue = false
  }

  console.log('处理后的仓库类型值:', commonValue) // 调试日志
  modelValue.value.common = commonValue
  // 不自动触发搜索，让用户手动点击搜索按钮
  console.log('仓库类型已更新，等待用户手动搜索') // 调试日志
}

function handleNameChange() {
  console.log('名称输入变化:', modelValue.value.name) // 调试日志
  // 输入框变化时延迟触发搜索，避免频繁请求
  clearTimeout((window as any).nameSearchTimer)
  ;(window as any).nameSearchTimer = setTimeout(() => {
    console.log('名称搜索触发，值:', modelValue.value.name) // 调试日志
    emit('search')
  }, 500)
}

function handleAddressChange() {
  console.log('地址输入变化:', modelValue.value.address) // 调试日志
  // 输入框变化时延迟触发搜索，避免频繁请求
  clearTimeout((window as any).addressSearchTimer)
  ;(window as any).addressSearchTimer = setTimeout(() => {
    console.log('地址搜索触发，值:', modelValue.value.address) // 调试日志
    emit('search')
  }, 500)
}

function clearNameSearch() {
  console.log('清除名称搜索') // 调试日志
  modelValue.value.name = ''
  emit('search')
}

function clearAddressSearch() {
  console.log('清除地址搜索') // 调试日志
  modelValue.value.address = ''
  emit('search')
}

function resetFilters() {
  console.log('重置筛选条件') // 调试日志
  modelValue.value = {
    name: '',
    address: '',
    enable: null,
    common: null,
    page: 1,
    limit: 10,
  }
  emit('reset')
}

// 获取显示文本
function getEnableText() {
  if (modelValue.value.enable === true) return '启用'
  if (modelValue.value.enable === false) return '禁用'
  return ''
}

function getCommonText() {
  if (modelValue.value.common === true) return '公共仓库'
  if (modelValue.value.common === false) return '私有仓库'
  return ''
}

// 组件卸载时清理定时器
onUnmounted(() => {
  clearTimeout((window as any).nameSearchTimer)
  clearTimeout((window as any).addressSearchTimer)
})

// 暴露方法给父组件
defineExpose({
  resetFilters,
  getFilters: () => modelValue.value,
  setFilters: (filters: FilterParams) => {
    modelValue.value = { ...modelValue.value, ...filters }
  },
  togglePanel: toggleFilterPanel,
})
</script>

<template>
  <view class="warehouse-filter">
    <!-- 筛选面板头部 -->
    <view
      class="filter-header"
      :style="showFilterPanel ? 'border-bottom: 2rpx solid rgba(0, 122, 255, 0.1)' : ''"
      @click="toggleFilterPanel"
    >
      <view class="filter-header-left">
        <wd-icon name="filter" size="32rpx" color="#007AFF" />
        <view class="filter-title">筛选条件</view>
        <view v-if="hasActiveFilters" class="filter-active-dot" />
      </view>
      <view class="filter-header-right">
        <wd-button type="text" size="small" custom-class="reset-button" @click.stop="resetFilters">
          重置
        </wd-button>
        <wd-icon
          :name="showFilterPanel ? 'arrow-up' : 'arrow-down'"
          size="32rpx"
          color="#8E8E93"
          class="collapse-icon"
        />
      </view>
    </view>

    <!-- 筛选面板内容 -->
    <view v-show="showFilterPanel" class="filter-content">
      <!-- 仓库名称搜索 -->
      <view class="filter-row">
        <view class="filter-item">
          <view class="search-input-wrapper">
            <view class="integrated-search-container">
              <view class="search-icon-section">
                <wd-icon name="search" size="32rpx" color="#8E8E93" />
              </view>
              <view class="search-divider" />
              <view class="search-input-section">
                <input
                  v-model="modelValue.name"
                  placeholder="请输入仓库名称"
                  class="search-input"
                  @input="handleNameChange"
                  @keydown.enter="handleSearch"
                />
                <view v-if="modelValue.name" class="clear-button" @click="clearNameSearch">
                  <wd-icon name="close-circle-fill" size="32rpx" color="#C7C7CC" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 仓库地址搜索 -->
      <view class="filter-row">
        <view class="filter-item">
          <view class="search-input-wrapper">
            <view class="integrated-search-container">
              <view class="search-icon-section">
                <wd-icon name="location" size="32rpx" color="#8E8E93" />
              </view>
              <view class="search-divider" />
              <view class="search-input-section">
                <input
                  v-model="modelValue.address"
                  placeholder="请输入仓库地址"
                  class="search-input"
                  @input="handleAddressChange"
                  @keydown.enter="handleSearch"
                />
                <view v-if="modelValue.address" class="clear-button" @click="clearAddressSearch">
                  <wd-icon name="close-circle-fill" size="32rpx" color="#C7C7CC" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 状态和类型筛选 -->
      <view class="filter-row">
        <view class="filter-group">
          <!-- 启用状态筛选 -->
          <view class="filter-item">
            <view class="filter-label">
              <wd-icon name="check-circle" size="28rpx" color="#007AFF" />
              <view class="label-text">启用状态</view>
            </view>
            <view class="picker-wrapper">
              <wd-picker
                :model-value="modelValue.enable === null ? '' : String(modelValue.enable)"
                :columns="[enableOptions]"
                placeholder="请选择状态"
                @confirm="handleEnableChange"
              >
                <view class="picker-trigger">
                  <view class="picker-text">
                    {{ getEnableText() || '请选择状态' }}
                  </view>
                  <wd-icon name="arrow-down" size="24rpx" color="#8E8E93" />
                </view>
              </wd-picker>
            </view>
          </view>

          <!-- 仓库类型筛选 -->
          <view class="filter-item">
            <view class="filter-label">
              <wd-icon name="share" size="28rpx" color="#007AFF" />
              <view class="label-text">仓库类型</view>
            </view>
            <view class="picker-wrapper">
              <wd-picker
                :model-value="modelValue.common === null ? '' : String(modelValue.common)"
                :columns="[commonOptions]"
                placeholder="请选择类型"
                @confirm="handleCommonChange"
              >
                <view class="picker-trigger">
                  <view class="picker-text">
                    {{ getCommonText() || '请选择类型' }}
                  </view>
                  <wd-icon name="arrow-down" size="24rpx" color="#8E8E93" />
                </view>
              </wd-picker>
            </view>
          </view>
        </view>
      </view>

      <!-- 搜索按钮 -->
      <view class="filter-actions">
        <wd-button type="primary" icon="search" custom-class="search-button" @click="handleSearch">
          搜索
        </wd-button>
        <wd-button icon="refresh" custom-class="reset-all-button" @click="resetFilters">
          重置
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统变量
$system-blue: #007aff;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(60, 60, 67, 0.36);

.warehouse-filter {
  background: $background-primary;
  border-radius: 32rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(255, 255, 255, 0.1);

  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 40rpx;
    background: rgba(0, 122, 255, 0.05);
    border-bottom: 2rpx solid rgba(0, 122, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      background: rgba(0, 122, 255, 0.1);
    }

    .filter-header-left {
      display: flex;
      align-items: center;
      gap: 20rpx;
      position: relative;

      .filter-title {
        font-size: 34rpx;
        font-weight: 600;
        color: $label-primary;
        letter-spacing: -0.4rpx;
      }

      .filter-active-dot {
        width: 16rpx;
        height: 16rpx;
        background: linear-gradient(45deg, $system-blue, #34c759);
        border-radius: 50%;
        margin-left: 16rpx;
        box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);
        animation: pulse 2s infinite;

        @keyframes pulse {
          0%,
          100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.8;
          }
        }
      }
    }

    .filter-header-right {
      display: flex;
      align-items: center;
      gap: 24rpx;

      :deep(.reset-button) {
        padding: 8rpx 24rpx;
        background: linear-gradient(135deg, rgba(255, 59, 48, 0.1) 0%, rgba(255, 149, 0, 0.1) 100%);
        border: 2rpx solid rgba(255, 59, 48, 0.2);
        border-radius: 28rpx;
        color: $system-red;
        font-size: 26rpx;
        font-weight: 500;
        transition: all 0.2s ease;

        &:active {
          background: linear-gradient(
            135deg,
            rgba(255, 59, 48, 0.2) 0%,
            rgba(255, 149, 0, 0.2) 100%
          );
          transform: scale(0.95);
        }
      }

      .collapse-icon {
        transition: all 0.3s ease;
      }
    }
  }

  .filter-content {
    padding: 40rpx;
    animation: filterExpand 0.3s ease;

    @keyframes filterExpand {
      from {
        opacity: 0;
        max-height: 0;
      }
      to {
        opacity: 1;
        max-height: 500px;
      }
    }
  }

  .filter-row {
    margin-bottom: 32rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .filter-item {
      .search-input-wrapper {
        .integrated-search-container {
          display: flex;
          align-items: stretch;
          background: $background-primary;
          border: 4rpx solid rgba(0, 122, 255, 0.1);
          border-radius: 32rpx;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);

          &:hover {
            border-color: rgba(0, 122, 255, 0.2);
            box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
          }

          &:focus-within {
            border-color: $system-blue;
            box-shadow: 0 0 0 8rpx rgba(0, 122, 255, 0.1);
          }

          .search-icon-section {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 28rpx 32rpx;
            background: linear-gradient(
              135deg,
              rgba(0, 122, 255, 0.05) 0%,
              rgba(52, 199, 89, 0.03) 100%
            );
            border-right: 2rpx solid rgba(0, 122, 255, 0.1);
            min-width: 120rpx;
          }

          .search-divider {
            width: 2rpx;
            background: linear-gradient(
              180deg,
              transparent 0%,
              rgba(0, 122, 255, 0.2) 50%,
              transparent 100%
            );
            margin: 16rpx 0;
          }

          .search-input-section {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 0 32rpx;
            gap: 24rpx;
            position: relative;

            .search-input {
              flex: 1;
              border: none;
              outline: none;
              font-size: 32rpx;
              color: $label-primary;
              background: transparent;
              padding: 28rpx 0;
              font-weight: 400;
              letter-spacing: -0.2rpx;

              &::placeholder {
                color: rgba(142, 142, 147, 0.8);
                font-weight: 400;
              }

              &:focus {
                &::placeholder {
                  opacity: 0.5;
                }
              }
            }

            .clear-button {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 48rpx;
              height: 48rpx;
              border-radius: 24rpx;
              cursor: pointer;
              transition: all 0.2s ease;
              flex-shrink: 0;

              &:active {
                transform: scale(0.9);
                background: rgba(199, 199, 204, 0.2);
              }
            }
          }
        }
      }
    }

    .filter-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32rpx;

      .filter-item {
        .filter-label {
          display: flex;
          align-items: center;
          gap: 12rpx;
          margin-bottom: 16rpx;

          .label-text {
            font-size: 28rpx;
            font-weight: 600;
            color: $label-primary;
          }
        }

        .picker-wrapper {
          .picker-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24rpx 32rpx;
            background: $background-primary;
            border: 4rpx solid rgba(0, 122, 255, 0.1);
            border-radius: 24rpx;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: rgba(0, 122, 255, 0.2);
              background: rgba(0, 122, 255, 0.02);
            }

            &:active {
              transform: scale(0.98);
            }

            .picker-text {
              font-size: 28rpx;
              color: $label-primary;
              font-weight: 500;

              &:empty::before {
                content: attr(placeholder);
                color: rgba(142, 142, 147, 0.8);
              }
            }
          }
        }
      }
    }
  }

  .filter-actions {
    display: flex;
    gap: 24rpx;
    margin-top: 40rpx;
    padding-top: 40rpx;
    border-top: 2rpx solid rgba(0, 122, 255, 0.1);

    :deep(.search-button) {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      padding: 24rpx 40rpx;
      background: linear-gradient(135deg, $system-blue 0%, #5ac8fa 100%);
      border: none;
      border-radius: 24rpx;
      color: white;
      font-size: 30rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);

      &:active {
        transform: scale(0.95);
        box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.4);
      }
    }

    :deep(.reset-all-button) {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      padding: 24rpx 40rpx;
      background: linear-gradient(
        135deg,
        rgba(142, 142, 147, 0.1) 0%,
        rgba(199, 199, 204, 0.1) 100%
      );
      border: 4rpx solid rgba(142, 142, 147, 0.2);
      border-radius: 24rpx;
      color: $system-gray;
      font-size: 30rpx;
      font-weight: 600;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: linear-gradient(
          135deg,
          rgba(142, 142, 147, 0.2) 0%,
          rgba(199, 199, 204, 0.2) 100%
        );
      }
    }
  }
}
</style>
