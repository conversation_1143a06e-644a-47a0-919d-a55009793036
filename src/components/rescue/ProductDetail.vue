<script setup lang="ts">
import type { IProduct } from '@/api/interface/rescue/product/product'
import { getProductDetailApi } from '@/api/modules/rescue/product/product'

interface Props {
  /** 物资ID */
  productId?: number
}

defineOptions({
  name: 'ProductDetail',
  options: {
    styleIsolation: 'shared',
  },
})

const modelValue = defineModel<boolean>()

const props = withDefaults(defineProps<Props>(), {
  productId: undefined,
})

const emit = defineEmits<{
  edit: [product: IProduct.Row]
  close: []
}>()

const loading = ref(false)
const productDetail = ref<IProduct.Row | null>(null)
const expandedSkus = ref<number[]>([])

// 方法
function close() {
  modelValue.value = false
  productDetail.value = null
  expandedSkus.value = []
  emit('close')
}

async function loadProductDetail(productId: number) {
  loading.value = true
  expandedSkus.value = []

  try {
    const response = await getProductDetailApi({ id: productId })
    productDetail.value = response.data || null
  } catch (error) {
    console.error('获取物资详情失败:', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'none',
    })
    close()
  } finally {
    loading.value = false
  }
}

// 监听外部状态变化
watch(
  modelValue,
  (newVal) => {
    if (newVal && props.productId) {
      loadProductDetail(props.productId)
    }
    if (!newVal) {
      productDetail.value = null
      expandedSkus.value = []
    }
  },
  { immediate: true },
)

// 监听产品ID变化
watch(
  () => props.productId,
  (newId) => {
    if (newId && modelValue.value) {
      loadProductDetail(newId)
    }
  },
)

async function open(productId: number) {
  modelValue.value = true
  await loadProductDetail(productId)
}

function onEdit() {
  if (productDetail.value) {
    emit('edit', productDetail.value)
  }
}

// 切换批次展开状态
function toggleLotExpand(skuIndex: number) {
  const index = expandedSkus.value.indexOf(skuIndex)
  if (index > -1) {
    expandedSkus.value.splice(index, 1)
  } else {
    expandedSkus.value.push(skuIndex)
  }
}

// 预览图片
function previewImage(url: string) {
  if (!url) return
  uni.previewImage({
    urls: [url],
    current: url,
  })
}

// 复制物资编号
function copyProductNumber(productNumber: string | undefined) {
  if (!productNumber) return

  uni.setClipboardData({
    data: productNumber,
    success: () => {
      uni.showToast({
        title: '编号已复制',
        icon: 'success',
        duration: 1500,
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 1500,
      })
    },
  })
}

// 获取标签样式
function getTagStyle(tag: IProduct.TagInfo) {
  if (!tag.backgroundColor && !tag.textColor) {
    return {
      backgroundColor: '#F2F2F7',
      color: '#8E8E93',
    }
  }

  if (tag.plain === 'true') {
    return {
      color: tag.backgroundColor || '#007AFF',
      borderColor: tag.backgroundColor || '#007AFF',
      backgroundColor: 'transparent',
      border: '2rpx solid',
    }
  } else {
    return {
      color: tag.textColor || '#FFFFFF',
      backgroundColor: tag.backgroundColor || '#007AFF',
    }
  }
}

// 格式化价格
function formatPrice(price: number | string | undefined) {
  if (price === undefined || price === null) return '0.00'
  return Number(price).toFixed(2)
}

// 格式化日期
function formatDate(date: string | undefined) {
  if (!date) return '无'
  try {
    return new Date(date).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  } catch {
    return date
  }
}

// 获取过期时间样式类
function getExpirationClass(expirationTime: string | undefined) {
  if (!expirationTime) return ''

  const now = new Date()
  const expiration = new Date(expirationTime)
  const diffDays = Math.ceil((expiration.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'expired'
  if (diffDays <= 30) return 'expiring-soon'
  return ''
}

// 获取报废日期样式类
function getDiscardClass(discardDate: string | undefined) {
  if (!discardDate) return ''

  const now = new Date()
  const discard = new Date(discardDate)
  const diffDays = Math.ceil((discard.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'discarded'
  if (diffDays <= 7) return 'discard-soon'
  return ''
}

// 获取检修时间样式类
function getOverhaulClass(overhaulDate: string | undefined) {
  if (!overhaulDate) return ''

  const now = new Date()
  const overhaul = new Date(overhaulDate)
  const diffDays = Math.ceil((overhaul.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'overdue'
  if (diffDays <= 7) return 'due-soon'
  return ''
}

// 暴露方法
defineExpose({
  open,
  close,
})
</script>

<template>
  <view>
    <!-- 外层view组件不能删除，否则微信小程样式会不生效！！！ -->
    <wd-popup
      v-model="modelValue"
      position="bottom"
      :close-on-click-modal="false"
      :safe-area-inset-bottom="true"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="99999"
      custom-class="product-detail-popup"
    >
      <view class="detail-container">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-overlay">
          <view class="loading-content">
            <wd-loading size="24" color="#007AFF" />
            <view class="loading-text">加载中...</view>
          </view>
        </view>
        <!-- 头部 -->
        <view class="detail-header">
          <view class="header-content">
            <view class="header-title">物资详情</view>
            <wd-icon name="close" size="20" class="close-btn" @click="close" />
          </view>
        </view>

        <!-- 详情内容 -->
        <scroll-view v-if="productDetail" class="detail-content" scroll-y :show-scrollbar="false">
          <view style="padding: 40rpx">
            <!-- 基本信息 -->
            <view class="info-section">
              <view class="section-header">
                <wd-icon name="info" size="16" color="#007AFF" />
                <view class="section-title">基本信息</view>
              </view>

              <!-- 物资基本信息卡片 -->
              <view class="product-card">
                <!-- 头部：图片、名称、状态 -->
                <view class="card-header">
                  <view class="product-image-wrapper">
                    <image
                      v-if="productDetail.image"
                      :src="productDetail.image"
                      class="product-image"
                      mode="aspectFill"
                      @click="previewImage(productDetail.image)"
                    />
                    <view v-else class="no-image">
                      <wd-icon name="image" size="24" color="#C7C7CC" />
                      <view class="no-image-text">无图片</view>
                    </view>
                  </view>

                  <view class="header-content">
                    <view class="product-name">
                      {{ productDetail.name || '未命名物资' }}
                    </view>
                    <view
                      v-if="productDetail.productNumber"
                      class="product-number-wrapper"
                      @click="copyProductNumber(productDetail.productNumber)"
                    >
                      <view class="product-number">
                        {{ productDetail.productNumber }}
                      </view>
                      <wd-icon name="copy" size="12" color="#007AFF" class="copy-icon" />
                    </view>
                    <view v-else class="no-number">无编号</view>
                    <view
                      class="status-tag"
                      :class="{ 'status-tag--disabled': !productDetail.enable }"
                    >
                      <view class="status-text">
                        {{ productDetail.enable ? '已启用' : '已禁用' }}
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 基本信息网格 -->
                <view class="info-grid">
                  <view class="info-item">
                    <view class="info-label">分类</view>
                    <view class="info-value">
                      {{ productDetail.categoryName || '无' }}
                    </view>
                  </view>

                  <view class="info-item">
                    <view class="info-label">仓库</view>
                    <view class="info-value">
                      {{ productDetail.warehouseName || '无' }}
                    </view>
                  </view>
                </view>

                <!-- 标签 -->
                <view
                  v-if="productDetail.tags && productDetail.tags.length > 0"
                  class="tags-section"
                >
                  <view class="tags-label">标签</view>
                  <view class="tags-list">
                    <view
                      v-for="tag in productDetail.tags"
                      :key="tag.id"
                      class="tag-item"
                      :style="getTagStyle(tag)"
                    >
                      <view class="tag-text">
                        {{ tag.name }}
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 规格信息 -->
            <view
              v-if="productDetail.productSkus && productDetail.productSkus.length > 0"
              class="info-section"
            >
              <view class="section-header">
                <wd-icon name="grid" size="16" color="#34C759" />
                <view class="section-title">规格信息 ({{ productDetail.productSkus.length }})</view>
              </view>

              <view class="sku-cards">
                <view
                  v-for="(sku, skuIndex) in productDetail.productSkus"
                  :key="sku.id"
                  class="sku-card"
                >
                  <!-- 规格基本信息 -->
                  <view class="sku-header">
                    <view class="sku-image-wrapper">
                      <image
                        v-if="sku.image"
                        :src="sku.image"
                        class="sku-image"
                        mode="aspectFill"
                        @click="previewImage(sku.image)"
                      />
                      <view v-else class="sku-no-image">
                        <wd-icon name="image" size="20" color="#C7C7CC" />
                      </view>
                    </view>

                    <view class="sku-info">
                      <view class="sku-name">
                        {{ sku.name || `规格 ${skuIndex + 1}` }}
                      </view>
                      <view class="sku-meta">
                        <view class="meta-item">
                          <view class="meta-label">价格:</view>
                          <view class="meta-value price-value">¥{{ formatPrice(sku.price) }}</view>
                        </view>
                        <view class="meta-item">
                          <view class="meta-label">状态:</view>
                          <view class="status-wrapper">
                            <view
                              class="status-dot"
                              :class="{ 'status-dot--disabled': !sku.enable }"
                            />
                            <view class="meta-value">
                              {{ sku.enable ? '启用' : '禁用' }}
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 批次信息 -->
                  <view v-if="sku.productLots && sku.productLots.length > 0" class="lots-section">
                    <view class="lots-header" @click="toggleLotExpand(skuIndex)">
                      <view class="lots-title">批次信息 ({{ sku.productLots.length }})</view>
                      <wd-icon
                        :name="expandedSkus.includes(skuIndex) ? 'arrow-up' : 'arrow-down'"
                        size="14"
                        color="#8E8E93"
                      />
                    </view>

                    <view v-show="expandedSkus.includes(skuIndex)" class="lots-content">
                      <view
                        v-for="(lot, lotIndex) in sku.productLots"
                        :key="lot.id"
                        class="lot-item"
                      >
                        <view class="lot-header">
                          <view class="lot-title">
                            {{ lot.title || `批次 ${lotIndex + 1}` }}
                          </view>
                          <view v-if="lot.lotNumber" class="lot-number">
                            {{ lot.lotNumber }}
                          </view>
                        </view>

                        <view class="lot-details">
                          <view class="detail-grid">
                            <view class="detail-item">
                              <view class="detail-label">价格</view>
                              <view class="detail-value">¥{{ formatPrice(lot.price) }}</view>
                            </view>
                            <view class="detail-item">
                              <view class="detail-label">入库数量</view>
                              <view class="detail-value">
                                {{ lot.quantity || 0 }}
                              </view>
                            </view>
                            <view class="detail-item">
                              <view class="detail-label">检修间隔</view>
                              <view class="detail-value">{{ lot.overhaulGap || 0 }}天</view>
                            </view>
                          </view>

                          <view class="date-grid">
                            <view class="date-item">
                              <view class="date-label">生产日期</view>
                              <view class="date-value">
                                {{ formatDate(lot.productDate) }}
                              </view>
                            </view>
                            <view class="date-item">
                              <view class="date-label">入库时间</view>
                              <view class="date-value">
                                {{ formatDate(lot.storageDate) }}
                              </view>
                            </view>
                            <view class="date-item">
                              <view class="date-label">过期时间</view>
                              <view
                                class="date-value"
                                :class="getExpirationClass(lot.expirationTime)"
                              >
                                {{ formatDate(lot.expirationTime) }}
                              </view>
                            </view>
                            <view class="date-item">
                              <view class="date-label">报废日期</view>
                              <view class="date-value" :class="getDiscardClass(lot.discardDate)">
                                {{ formatDate(lot.discardDate) }}
                              </view>
                            </view>
                            <view class="date-item">
                              <view class="date-label">上次检修</view>
                              <view class="date-value">
                                {{ formatDate(lot.overhaulLastDate) }}
                              </view>
                            </view>
                            <view class="date-item">
                              <view class="date-label">下次检修</view>
                              <view
                                class="date-value"
                                :class="getOverhaulClass(lot.overhaulNextDate)"
                              >
                                {{ formatDate(lot.overhaulNextDate) }}
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>

                  <view v-else class="no-lots">
                    <view class="no-lots-text">暂无批次信息</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 时间信息 -->
            <view class="info-section">
              <view class="section-header">
                <wd-icon name="time" size="16" color="#FF9500" />
                <view class="section-title">时间信息</view>
              </view>

              <view class="time-info">
                <view class="time-item">
                  <view class="time-label">创建时间</view>
                  <view class="time-value">
                    {{ formatDate(productDetail.createTime) }}
                  </view>
                </view>
                <view class="time-item">
                  <view class="time-label">更新时间</view>
                  <view class="time-value">
                    {{ formatDate(productDetail.updateTime) }}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部操作按钮 -->
        <view class="detail-footer">
          <wd-button custom-class="footer-btn cancel-btn flex-1" type="error" @click="close">
            关闭
          </wd-button>
          <wd-button type="primary" custom-class="footer-btn edit-btn flex-1" @click="onEdit">
            编辑
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统变量
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(60, 60, 67, 0.36);

:deep(.product-detail-popup) {
  border-radius: 32rpx 32rpx 0 0;
  // 确保弹窗占满屏幕
  :deep(.wd-popup) {
    z-index: 9999;
  }

  // 全局移动端优化
  .detail-container {
    box-sizing: border-box;
  }

  // 防止任何元素超出容器宽度
  :deep(.detail-container) {
    max-width: 100%;
  }

  .detail-container {
    background-color: $background-primary;
    border-radius: 32rpx 32rpx 0 0;
    height: 85vh; // 固定高度，确保不溢出
    display: flex;
    flex-direction: column;
    position: relative;
    width: 100%;
    max-width: 100vw; // 确保不超过视口宽度
    overflow: hidden; // 防止内容溢出
    box-sizing: border-box;

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      border-radius: 32rpx 32rpx 0 0;

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24rpx;

        .loading-text {
          font-size: 28rpx;
          color: $system-gray;
          font-weight: 500;
        }
      }
    }

    .detail-header {
      padding: 32rpx 40rpx;
      border-bottom: 2rpx solid $separator;
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(52, 199, 89, 0.03) 100%);
      flex-shrink: 0; // 防止头部被压缩

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .header-title {
          font-size: 36rpx;
          font-weight: 600;
          color: $label-primary;
        }

        .close-btn {
          padding: 12rpx;
          color: $system-gray;
          transition: all 0.2s ease;
          border-radius: 16rpx;

          &:active {
            color: $label-primary;
            background-color: rgba(0, 0, 0, 0.05);
          }
        }
      }
    }

    .detail-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden; // 防止水平滚动
      -webkit-overflow-scrolling: touch; // iOS 平滑滚动
      width: 100%;
      box-sizing: border-box;
      scrollbar-color: #0000;
      scrollbar-width: 0;

      ::-webkit-scrollbar {
        display: none;
      }

      .info-section {
        margin-bottom: 40rpx;

        .section-header {
          display: flex;
          align-items: center;
          gap: 16rpx;
          margin-bottom: 24rpx;
          padding: 16rpx 0;
          border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);

          .section-title {
            font-size: 32rpx;
            font-weight: 600;
            color: $label-primary;
          }
        }

        .product-card {
          background: $background-secondary;
          border-radius: 24rpx;
          padding: 32rpx;
          margin-bottom: 32rpx;

          .card-header {
            display: flex;
            gap: 24rpx;
            align-items: flex-start;
            margin-bottom: 32rpx;

            .product-image-wrapper {
              width: 160rpx;
              height: 160rpx;
              flex-shrink: 0;
              border-radius: 16rpx;
              overflow: hidden;
              box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);

              .product-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .no-image {
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background-color: rgba(199, 199, 204, 0.2);
                border: 2rpx dashed rgba(199, 199, 204, 0.5);

                .no-image-text {
                  margin-top: 8rpx;
                  font-size: 24rpx;
                  color: $system-gray;
                }
              }
            }

            .header-content {
              flex: 1;
              min-width: 0;
              position: relative;

              .product-name {
                font-size: 36rpx;
                font-weight: 600;
                color: $label-primary;
                line-height: 1.3;
                margin-bottom: 16rpx;
                padding-right: 140rpx; // 为状态标签留出空间
              }

              .product-number-wrapper {
                display: flex;
                align-items: center;
                gap: 12rpx;
                margin-bottom: 16rpx;
                padding: 12rpx 20rpx;
                background: linear-gradient(
                  135deg,
                  rgba(0, 122, 255, 0.08) 0%,
                  rgba(0, 122, 255, 0.04) 100%
                );
                border: 2rpx solid rgba(0, 122, 255, 0.15);
                border-radius: 16rpx;
                cursor: pointer;
                transition: all 0.2s ease;
                max-width: fit-content;

                &:active {
                  transform: scale(0.98);
                  background: linear-gradient(
                    135deg,
                    rgba(0, 122, 255, 0.12) 0%,
                    rgba(0, 122, 255, 0.08) 100%
                  );
                  border-color: rgba(0, 122, 255, 0.25);
                }

                .product-number {
                  font-size: 26rpx;
                  font-weight: 500;
                  color: $system-blue;
                  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                  letter-spacing: 1rpx;
                }

                .copy-icon {
                  opacity: 0.7;
                  transition: opacity 0.2s ease;
                }

                &:hover .copy-icon {
                  opacity: 1;
                }
              }

              .no-number {
                font-size: 26rpx;
                color: $system-gray;
                font-style: italic;
                margin-bottom: 16rpx;
              }

              .status-tag {
                position: absolute;
                top: 0;
                right: 0;
                padding: 8rpx 16rpx;
                border-radius: 24rpx;
                background-color: $system-green;
                box-shadow: 0 4rpx 8rpx rgba(52, 199, 89, 0.2);

                &--disabled {
                  background-color: $system-gray;
                  box-shadow: 0 4rpx 8rpx rgba(142, 142, 147, 0.2);
                }

                .status-text {
                  font-size: 22rpx;
                  font-weight: 600;
                  color: white;
                  line-height: 1.2;
                }
              }
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24rpx;
            margin-bottom: 32rpx;

            .info-item {
              display: flex;
              flex-direction: column;
              gap: 8rpx;

              .info-label {
                font-size: 24rpx;
                color: $label-secondary;
                font-weight: 500;
              }

              .info-value {
                font-size: 28rpx;
                color: $label-primary;
                font-weight: 500;
                line-height: 1.2;
              }
            }
          }

          .tags-section {
            .tags-label {
              display: block;
              font-size: 24rpx;
              color: $label-secondary;
              font-weight: 500;
              margin-bottom: 16rpx;
            }

            .tags-list {
              display: flex;
              flex-wrap: wrap;
              gap: 12rpx;

              .tag-item {
                padding: 8rpx 16rpx;
                border-radius: 16rpx;
                background-color: white;
                border: 2rpx solid rgba(0, 0, 0, 0.05);

                .tag-text {
                  font-size: 22rpx;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      .sku-cards {
        .sku-card {
          background-color: $background-secondary;
          border-radius: 24rpx;
          padding: 24rpx;
          margin-bottom: 24rpx;
          border: 2rpx solid rgba(0, 0, 0, 0.05);

          &:last-child {
            margin-bottom: 0;
          }

          .sku-header {
            display: flex;
            gap: 24rpx;
            margin-bottom: 24rpx;
            align-items: flex-start;

            .sku-image-wrapper {
              width: 120rpx;
              height: 120rpx;
              border-radius: 16rpx;
              overflow: hidden;
              flex-shrink: 0;

              .sku-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .sku-no-image {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(199, 199, 204, 0.2);
                border: 2rpx dashed rgba(199, 199, 204, 0.5);
              }
            }

            .sku-info {
              flex: 1;
              min-width: 0;

              .sku-name {
                display: block;
                font-size: 30rpx;
                font-weight: 600;
                color: $label-primary;
                margin-bottom: 16rpx;
              }

              .sku-meta {
                display: flex;
                flex-direction: column;
                gap: 8rpx;

                .meta-item {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  .meta-label {
                    font-size: 26rpx;
                    color: $label-secondary;
                    min-width: 80rpx;
                    font-weight: 500;
                  }

                  .meta-value {
                    font-size: 26rpx;
                    font-weight: 500;
                    text-align: right;

                    &.price-value {
                      color: $system-orange;
                    }
                  }

                  .status-wrapper {
                    display: flex;
                    align-items: center;
                    gap: 8rpx;

                    .status-dot {
                      width: 12rpx;
                      height: 12rpx;
                      border-radius: 50%;
                      background-color: $system-green;

                      &--disabled {
                        background-color: $system-gray;
                      }
                    }
                  }
                }
              }
            }
          }

          .lots-section {
            .lots-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 16rpx 0;
              border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);
              cursor: pointer;

              .lots-title {
                font-size: 28rpx;
                font-weight: 600;
                color: $label-primary;
              }
            }

            .lots-content {
              margin-top: 16rpx;

              .lot-item {
                background-color: $background-primary;
                border-radius: 16rpx;
                padding: 16rpx;
                margin-bottom: 12rpx;

                &:last-child {
                  margin-bottom: 0;
                }

                .lot-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 16rpx;

                  .lot-title {
                    font-size: 26rpx;
                    font-weight: 600;
                    color: $label-primary;
                  }

                  .lot-number {
                    font-size: 22rpx;
                    color: $system-gray;
                    background-color: rgba(142, 142, 147, 0.1);
                    padding: 4rpx 12rpx;
                    border-radius: 12rpx;
                  }
                }

                .lot-details {
                  .detail-grid {
                    display: grid;
                    grid-template-columns: 1fr; // 移动端单列布局
                    gap: 12rpx;
                    margin-bottom: 16rpx;

                    .detail-item {
                      display: flex;
                      justify-content: space-between; // 移动端使用水平对齐
                      align-items: center;
                      padding: 8rpx 0;
                      border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);

                      .detail-label {
                        font-size: 22rpx;
                        color: $label-secondary;
                        font-weight: 500;
                        min-width: 120rpx;
                      }

                      .detail-value {
                        font-size: 24rpx;
                        font-weight: 500;
                        color: $label-primary;
                        text-align: right;
                      }
                    }
                  }

                  .date-grid {
                    display: grid;
                    grid-template-columns: 1fr; // 移动端单列布局
                    gap: 8rpx;

                    .date-item {
                      display: flex;
                      justify-content: space-between; // 移动端使用水平对齐
                      align-items: center;
                      padding: 8rpx 0;
                      border-bottom: 2rpx solid rgba(0, 0, 0, 0.05);

                      .date-label {
                        font-size: 22rpx;
                        color: $label-secondary;
                        font-weight: 500;
                        min-width: 120rpx;
                      }

                      .date-value {
                        font-size: 22rpx;
                        font-weight: 500;
                        color: $label-primary;
                        text-align: right;

                        &.expired,
                        &.discarded,
                        &.overdue {
                          color: $system-red;
                        }

                        &.expiring-soon,
                        &.discard-soon,
                        &.due-soon {
                          color: $system-orange;
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .no-lots {
            text-align: center;
            padding: 32rpx;

            .no-lots-text {
              font-size: 26rpx;
              color: $system-gray;
            }
          }
        }
      }

      .time-info {
        .time-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24rpx 0;
          border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);

          &:last-child {
            border-bottom: none;
          }

          .time-label {
            font-size: 30rpx;
            color: $label-secondary;
            font-weight: 500;
          }

          .time-value {
            font-size: 30rpx;
            color: $label-primary;
            font-weight: 500;
          }
        }
      }
    }
  }

  .detail-footer {
    display: flex;
    gap: 24rpx;
    padding: 24rpx 40rpx;
    border-top: 2rpx solid $separator;
    background-color: $background-primary;
    flex-shrink: 0; // 防止底部被压缩

    :deep(.footer-btn) {
      flex: 1;
      padding: 28rpx 0;
      border-radius: 24rpx;
      font-weight: 600;
      font-size: 32rpx;
      transition: all 0.2s ease;

      &.cancel-btn {
        background-color: $background-secondary;
        color: $label-primary;

        &:active {
          background-color: darken($background-secondary, 5%);
          transform: scale(0.98);
        }
      }

      &.edit-btn {
        background-color: $system-blue;
        color: white;

        &:active {
          background-color: darken($system-blue, 10%);
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
