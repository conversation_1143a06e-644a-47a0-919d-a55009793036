<script setup lang="ts">
import type { IMetaWarehouse } from '@/api/interface/rescue/meta/metaWarehouse'

const props = defineProps<{
  warehouse: IMetaWarehouse.Row | null
}>()

const emit = defineEmits<{
  submit: [formData: IMetaWarehouse.CreateForm | IMetaWarehouse.UpdateForm]
  cancel: []
}>()

const formRef = ref()

// 判断是否为编辑模式
const isEdit = computed(() => !!props.warehouse)

// 表单数据
const formData = reactive<IMetaWarehouse.CreateForm | IMetaWarehouse.UpdateForm>({
  id: props.warehouse?.id || 0,
  name: props.warehouse?.name || '',
  address: props.warehouse?.address || '',
  detailAddress: props.warehouse?.detailAddress || '',
  location: props.warehouse?.location || '',
  longitude: props.warehouse?.longitude || '',
  latitude: props.warehouse?.latitude || '',
  enable: props.warehouse?.enable ?? true,
  common: props.warehouse?.common ?? false,
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
  detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
}

// 关闭弹窗
function handleClose() {
  emit('cancel')
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value.validate()

    const submitData = { ...formData }

    // 清理空值
    Object.keys(submitData).forEach((key) => {
      if (submitData[key] === '' || submitData[key] === null || submitData[key] === undefined) {
        delete submitData[key]
      }
    })

    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<template>
  <view class="warehouse-form">
    <!-- 头部 -->
    <view class="form-header">
      <view class="header-left">
        <wd-icon name="edit" size="36rpx" color="#007AFF" />
        <view class="form-title">
          {{ isEdit ? '编辑仓库' : '新增仓库' }}
        </view>
      </view>
      <view class="header-right">
        <wd-button type="text" icon="close" custom-class="close-btn" @click="handleClose" />
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content">
      <wd-form ref="formRef" :model="formData" :rules="rules" label-width="140rpx">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="section-title">
            <wd-icon name="info" size="28rpx" color="#007AFF" />
            <view>基本信息</view>
          </view>
          <view class="section-content">
            <wd-form-item label="仓库名称" prop="name">
              <wd-input v-model="formData.name" placeholder="请输入仓库名称" clearable no-border />
            </wd-form-item>
            <wd-form-item label="仓库地址" prop="address">
              <wd-input
                v-model="formData.address"
                placeholder="请输入仓库地址"
                clearable
                no-border
              />
            </wd-form-item>
            <wd-form-item label="详细地址" prop="detailAddress">
              <wd-input
                v-model="formData.detailAddress"
                placeholder="请输入详细地址"
                clearable
                no-border
              />
            </wd-form-item>
          </view>
        </view>

        <!-- 状态设置 -->
        <view class="form-section">
          <view class="section-title">
            <wd-icon name="setting" size="28rpx" color="#FF9500" />
            <view>状态设置</view>
          </view>
          <view class="section-content">
            <view class="switch-row">
              <view class="switch-item">
                <view class="switch-label">仓库状态</view>
                <wd-switch v-model="formData.enable" />
                <view class="switch-text">
                  {{ formData.enable ? '启用' : '禁用' }}
                </view>
              </view>
              <view class="switch-item">
                <view class="switch-label">仓库类型</view>
                <wd-switch v-model="formData.common" />
                <view class="switch-text">
                  {{ formData.common ? '公共仓库' : '私有仓库' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </wd-form>
    </view>

    <!-- 底部操作 -->
    <view class="form-footer">
      <wd-button type="error" icon="close" @click="handleClose">取消</wd-button>
      <wd-button type="primary" icon="check" @click="handleSubmit">
        {{ isEdit ? '更新' : '创建' }}
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* Apple 设计系统变量 */
:root {
  --apple-blue: #007aff;
  --apple-green: #34c759;
  --apple-orange: #ff9500;
  --apple-red: #ff3b30;
  --apple-gray: #8e8e93;
  --apple-gray-light: #c7c7cc;
  --apple-gray-dark: #48484a;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-radius: 24rpx;
  --apple-radius-small: 16rpx;
  --apple-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  --apple-shadow-elevated: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.warehouse-form {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--apple-surface);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.form-header {
  background: var(--apple-surface);
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid var(--apple-gray-light);
  box-shadow: var(--apple-shadow);
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .form-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1d1d1f;
      line-height: 1.4;
    }
  }

  .header-right {
    :deep(.close-btn) {
      padding: 12rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;

      &:active {
        background: rgba(142, 142, 147, 0.1);
        transform: scale(0.95);
      }
    }
  }
}

.form-content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.form-section {
  background: var(--apple-surface);
  border-radius: var(--apple-radius);
  border: 1rpx solid var(--apple-gray-light);
  overflow: hidden;
  box-shadow: var(--apple-shadow);

  .section-title {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(52, 199, 89, 0.03) 100%);
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    border-bottom: 1rpx solid var(--apple-gray-light);

    text {
      font-size: 26rpx;
      font-weight: 600;
      color: #1d1d1f;
    }
  }

  .section-content {
    padding: 16rpx 24rpx;
  }

  .switch-row {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .switch-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12rpx 0;
      border-bottom: 1rpx solid rgba(142, 142, 147, 0.1);

      &:last-child {
        border-bottom: none;
      }

      .switch-label {
        font-size: 24rpx;
        color: var(--apple-gray-dark);
        font-weight: 500;
        flex: 1;
      }

      .switch-text {
        font-size: 22rpx;
        color: var(--apple-gray);
        margin-left: 16rpx;
        min-width: 100rpx;
        text-align: right;
      }
    }
  }
}

.form-footer {
  background: var(--apple-surface);
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--apple-gray-light);
  display: flex;
  gap: 16rpx;
  justify-content: center;
  flex-shrink: 0;
}
</style>
