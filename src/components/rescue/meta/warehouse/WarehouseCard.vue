<script setup lang="ts">
import type { IMetaWarehouse } from '@/api/interface/rescue/meta/metaWarehouse'

interface Props {
  /** 仓库数据 */
  warehouse: IMetaWarehouse.Row
  /** 是否选中 */
  selected?: boolean
  /** 是否显示复选框 */
  showCheckbox?: boolean
}

defineOptions({
  name: 'WarehouseCard',
  options: {
    styleIsolation: 'shared',
  },
})

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  showCheckbox: false,
})

const emit = defineEmits<{
  cardClick: [warehouse: IMetaWarehouse.Row]
  selectChange: [selected: boolean]
  viewDetail: [warehouse: IMetaWarehouse.Row]
  edit: [warehouse: IMetaWarehouse.Row]
  delete: [warehouse: IMetaWarehouse.Row]
}>()

// 卡片点击
function onCardClick() {
  emit('cardClick', props.warehouse)
}

// 选择变化
function onSelectChange(checked: boolean) {
  emit('selectChange', checked)
}

// 点击复选框区域
function onCheckboxClick(event: Event) {
  event.stopPropagation()
  onSelectChange(!props.selected)
}

// 查看详情
function onViewDetail() {
  emit('viewDetail', props.warehouse)
}

// 编辑
function onEdit() {
  emit('edit', props.warehouse)
}

// 删除
function onDelete() {
  emit('delete', props.warehouse)
}

// 获取仓库图标
function getWarehouseIcon() {
  if (props.warehouse.common) {
    return 'share'
  }
  return 'location'
}

// 获取图标容器样式类
function getIconContainerClass() {
  if (props.warehouse.common) {
    return 'icon-container--public'
  }
  return 'icon-container--private'
}

// 获取图标颜色
function getIconColor() {
  if (props.warehouse.common) {
    return '#007AFF'
  }
  return '#FF9500'
}

// 获取左侧彩色条样式
function getAccentClass() {
  if (props.warehouse.common) {
    return 'card-accent--public'
  }
  return 'card-accent--private'
}

// 获取标签样式类
function getTagClass(type: 'enable' | 'common') {
  if (type === 'enable') {
    return props.warehouse.enable ? 'tag-item--enabled' : 'tag-item--disabled'
  } else {
    return props.warehouse.common ? 'tag-item--public' : 'tag-item--private'
  }
}

// 获取标签颜色
function getTagColor(type: 'enable' | 'common') {
  if (type === 'enable') {
    return props.warehouse.enable ? '#34C759' : '#FF3B30'
  } else {
    return props.warehouse.common ? '#007AFF' : '#FF9500'
  }
}
</script>

<template>
  <view class="warehouse-card" :class="{ selected }" @click="onCardClick">
    <!-- 右上角复选框 -->
    <view v-if="showCheckbox" class="checkbox-wrapper" @click="onCheckboxClick">
      <view class="custom-checkbox" :class="{ 'custom-checkbox--checked': selected }">
        <wd-icon v-if="selected" name="check" size="14" color="white" class="check-icon" />
      </view>
    </view>

    <!-- 左侧彩色条 -->
    <view class="card-accent" :class="getAccentClass()" />

    <!-- 主要内容 -->
    <view class="card-content">
      <!-- 左侧图标区域 -->
      <view class="icon-wrapper">
        <view class="icon-container" :class="getIconContainerClass()">
          <wd-icon
            :name="getWarehouseIcon()"
            size="24"
            :color="getIconColor()"
            class="warehouse-icon"
          />
        </view>

        <!-- 状态标识 -->
        <view class="status-badge" :class="{ 'status-badge--disabled': !warehouse.enable }">
          <wd-icon
            :name="warehouse.enable ? 'check' : 'close'"
            size="10"
            color="white"
            class="status-icon"
          />
          <view class="status-text">
            {{ warehouse.enable ? '启用' : '禁用' }}
          </view>
        </view>
      </view>

      <!-- 右侧信息区域 -->
      <view class="info-section">
        <!-- 头部信息 -->
        <view class="info-header">
          <view class="warehouse-name">
            {{ warehouse.name || '未命名仓库' }}
          </view>
        </view>

        <!-- 地址信息 -->
        <view class="address-info">
          <view v-if="warehouse.address" class="address-item">
            <wd-icon name="location" size="12" color="#8E8E93" />
            <view class="address-text">
              {{ warehouse.address }}
            </view>
          </view>
          <view v-if="warehouse.detailAddress" class="address-item">
            <wd-icon name="map" size="12" color="#8E8E93" />
            <view class="detail-address-text">
              {{ warehouse.detailAddress }}
            </view>
          </view>
        </view>

        <!-- 标签信息 -->
        <view class="tags-section">
          <view class="tag-item" :class="getTagClass('enable')">
            <wd-icon
              :name="warehouse.enable ? 'check-circle' : 'close-circle'"
              size="12"
              :color="getTagColor('enable')"
            />
            <view class="tag-text">
              {{ warehouse.enable ? '启用' : '禁用' }}
            </view>
          </view>
          <view class="tag-item" :class="getTagClass('common')">
            <wd-icon
              :name="warehouse.common ? 'share' : 'lock'"
              size="12"
              :color="getTagColor('common')"
            />
            <view class="tag-text">
              {{ warehouse.common ? '公共仓库' : '私有仓库' }}
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <wd-button
            size="small"
            type="text"
            icon="eye-close"
            custom-class="action-btn view-btn"
            @click.stop="onViewDetail"
          >
            详情
          </wd-button>
          <wd-button
            size="small"
            type="text"
            icon="edit"
            custom-class="action-btn edit-btn"
            @click.stop="onEdit"
          >
            编辑
          </wd-button>
          <wd-button
            size="small"
            type="text"
            icon="delete"
            custom-class="action-btn delete-btn"
            @click.stop="onDelete"
          >
            删除
          </wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// Apple 设计系统变量
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$separator: rgba(60, 60, 67, 0.36);

.warehouse-card {
  background-color: $background-primary;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  position: relative;
  margin-bottom: 32rpx;

  &.selected {
    border: 4rpx solid $system-blue;
    background-color: rgba(0, 122, 255, 0.05);
    box-shadow: 0 0 0 6rpx rgba(0, 122, 255, 0.1);
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.12);
  }

  &:hover {
    box-shadow: 0 16rpx 50rpx rgba(0, 0, 0, 0.15);
    transform: translateY(-4rpx);
  }

  // 右上角复选框
  .checkbox-wrapper {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    z-index: 10;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.9);
    }

    .custom-checkbox {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      border: 4rpx solid rgba(0, 122, 255, 0.3);
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(20rpx);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);

      &--checked {
        background: linear-gradient(135deg, $system-blue 0%, #5ac8fa 100%);
        border-color: $system-blue;
        box-shadow: 0 4rpx 24rpx rgba(0, 122, 255, 0.3);

        .check-icon {
          animation: checkBounce 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }
      }

      .check-icon {
        transition: all 0.2s ease;
      }
    }
  }

  @keyframes checkBounce {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // 左侧彩色条
  .card-accent {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 8rpx;
    border-radius: 4rpx 0 0 4rpx;

    &--public {
      background: linear-gradient(135deg, $system-blue 0%, #5ac8fa 100%);
    }

    &--private {
      background: linear-gradient(135deg, $system-orange 0%, #ffcc02 100%);
    }
  }

  .card-content {
    display: flex;
    gap: 32rpx;
    align-items: flex-start;
    margin-left: 16rpx;
  }

  .icon-wrapper {
    position: relative;
    flex-shrink: 0;
  }

  .icon-container {
    width: 120rpx;
    height: 120rpx;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;

    &--public {
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.1) 0%, rgba(90, 200, 250, 0.1) 100%);
      border: 4rpx solid rgba(0, 122, 255, 0.2);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(0, 122, 255, 0.05) 0%,
          rgba(90, 200, 250, 0.05) 100%
        );
        border-radius: 28rpx;
      }
    }

    &--private {
      background: linear-gradient(135deg, rgba(255, 149, 0, 0.1) 0%, rgba(255, 204, 2, 0.1) 100%);
      border: 4rpx solid rgba(255, 149, 0, 0.2);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 149, 0, 0.05) 0%,
          rgba(255, 204, 2, 0.05) 100%
        );
        border-radius: 28rpx;
      }
    }

    .warehouse-icon {
      position: relative;
      z-index: 1;
    }
  }

  .status-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    padding: 4rpx 12rpx;
    border-radius: 16rpx;
    background-color: rgba(52, 199, 89, 0.9);
    backdrop-filter: blur(20rpx);
    display: flex;
    align-items: center;
    gap: 4rpx;
    border: 2rpx solid rgba(255, 255, 255, 0.3);

    .status-icon {
      flex-shrink: 0;
    }

    .status-text {
      font-size: 18rpx;
      color: white;
      font-weight: 600;
    }

    &--disabled {
      background-color: rgba(142, 142, 147, 0.9);
    }
  }

  .info-section {
    flex: 1;
    min-width: 0;
  }

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;

    .warehouse-name {
      font-size: 36rpx;
      font-weight: 600;
      color: $label-primary;
      line-height: 1.3;
      flex: 1;
      // 文本省略处理
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .address-info {
    margin-bottom: 24rpx;

    .address-item {
      display: flex;
      align-items: flex-start;
      gap: 12rpx;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .address-text {
        font-size: 28rpx;
        color: $label-secondary;
        line-height: 1.4;
        flex: 1;
        // 文本省略处理
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .detail-address-text {
        font-size: 26rpx;
        color: $system-gray;
        line-height: 1.4;
        flex: 1;
        // 文本省略处理
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .tags-section {
    display: flex;
    gap: 16rpx;
    margin-bottom: 24rpx;
    flex-wrap: wrap;

    .tag-item {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 8rpx 16rpx;
      border-radius: 24rpx;
      font-size: 22rpx;
      font-weight: 500;
      border: 2rpx solid;

      .tag-text {
        font-size: 22rpx;
        font-weight: 500;
      }

      &--enabled {
        background-color: rgba(52, 199, 89, 0.1);
        border-color: rgba(52, 199, 89, 0.2);
        .tag-text {
          color: $system-green;
        }
      }

      &--disabled {
        background-color: rgba(255, 59, 48, 0.1);
        border-color: rgba(255, 59, 48, 0.2);
        .tag-text {
          color: $system-red;
        }
      }

      &--public {
        background-color: rgba(0, 122, 255, 0.1);
        border-color: rgba(0, 122, 255, 0.2);
        .tag-text {
          color: $system-blue;
        }
      }

      &--private {
        background-color: rgba(255, 149, 0, 0.1);
        border-color: rgba(255, 149, 0, 0.2);
        .tag-text {
          color: $system-orange;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 16rpx;
    padding-top: 24rpx;
    border-top: 2rpx solid rgba(0, 0, 0, 0.06);
    margin-top: 16rpx;

    :deep(.action-btn) {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      padding: 16rpx 24rpx;
      border-radius: 20rpx;
      transition: all 0.2s ease;
      font-weight: 500;
      min-height: 64rpx;
      font-size: 24rpx;
      border: 2rpx solid;

      &.view-btn {
        color: $system-blue;
        background-color: rgba(0, 122, 255, 0.1);
        border-color: rgba(0, 122, 255, 0.2);

        &:active {
          background-color: rgba(0, 122, 255, 0.2);
          transform: scale(0.98);
        }
      }

      &.edit-btn {
        color: $system-orange;
        background-color: rgba(255, 149, 0, 0.1);
        border-color: rgba(255, 149, 0, 0.2);

        &:active {
          background-color: rgba(255, 149, 0, 0.2);
          transform: scale(0.98);
        }
      }

      &.delete-btn {
        color: $system-red;
        background-color: rgba(255, 59, 48, 0.1);
        border-color: rgba(255, 59, 48, 0.2);

        &:active {
          background-color: rgba(255, 59, 48, 0.2);
          transform: scale(0.98);
        }
      }
    }
  }
}
</style>
