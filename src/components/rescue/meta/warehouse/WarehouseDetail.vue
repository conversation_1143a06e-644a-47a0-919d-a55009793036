<script setup lang="ts">
import type { IMetaWarehouse } from '@/api/interface/rescue/meta/metaWarehouse'

defineProps<{
  warehouse: IMetaWarehouse.Row | null
}>()

const emit = defineEmits<{
  close: []
}>()

// 关闭弹窗
function handleClose() {
  emit('close')
}
</script>

<template>
  <view class="warehouse-detail">
    <!-- 头部 -->
    <view class="detail-header">
      <view class="header-left">
        <wd-icon name="warehouse" size="36rpx" color="#007AFF" />
        <view class="detail-title">仓库详情</view>
      </view>
      <view class="header-right">
        <wd-button type="text" icon="close" custom-class="close-btn" @click="handleClose" />
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="detail-content">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">
          <wd-icon name="info" size="28rpx" color="#007AFF" />
          <view>基本信息</view>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">仓库名称</view>
            <view class="info-value">
              {{ warehouse?.name || '暂无' }}
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">仓库地址</view>
            <view class="info-value">
              {{ warehouse?.address || '暂无' }}
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">详细地址</view>
            <view class="info-value">
              {{ warehouse?.detailAddress || '暂无' }}
            </view>
          </view>
        </view>
      </view>

      <!-- 状态信息 -->
      <view class="info-section">
        <view class="section-title">
          <wd-icon name="setting" size="28rpx" color="#FF9500" />
          <view>状态信息</view>
        </view>
        <view class="status-row">
          <view class="status-item">
            <view class="status-label">仓库状态</view>
            <view class="status-tag" :class="{ 'status-tag--enabled': warehouse?.enable }">
              <wd-icon
                :name="warehouse?.enable ? 'check-circle' : 'close-circle'"
                size="24rpx"
                :color="warehouse?.enable ? '#34C759' : '#FF3B30'"
              />
              <view class="status-text">
                {{ warehouse?.enable ? '启用' : '禁用' }}
              </view>
            </view>
          </view>
          <view class="status-item">
            <view class="status-label">仓库类型</view>
            <view class="status-tag" :class="{ 'status-tag--public': warehouse?.common }">
              <wd-icon
                :name="warehouse?.common ? 'share' : 'lock'"
                size="24rpx"
                :color="warehouse?.common ? '#007AFF' : '#FF9500'"
              />
              <view class="status-text">
                {{ warehouse?.common ? '公共仓库' : '私有仓库' }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="info-section">
        <view class="section-title">
          <wd-icon name="clock" size="28rpx" color="#AF52DE" />
          <view>时间信息</view>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">创建时间</view>
            <view class="info-value">
              {{ warehouse?.createTime || '暂无' }}
            </view>
          </view>
          <view class="info-item">
            <view class="info-label">更新时间</view>
            <view class="info-value">
              {{ warehouse?.updateTime || '暂无' }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作 -->
    <view class="detail-footer">
      <wd-button type="error" icon="close" @click="handleClose">关闭</wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* Apple 设计系统变量 */
:root {
  --apple-blue: #007aff;
  --apple-green: #34c759;
  --apple-orange: #ff9500;
  --apple-purple: #af52de;
  --apple-red: #ff3b30;
  --apple-gray: #8e8e93;
  --apple-gray-light: #c7c7cc;
  --apple-gray-dark: #48484a;
  --apple-background: #f2f2f7;
  --apple-surface: #ffffff;
  --apple-radius: 24rpx;
  --apple-radius-small: 16rpx;
  --apple-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  --apple-shadow-elevated: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
}

.warehouse-detail {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--apple-surface);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  background: var(--apple-surface);
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid var(--apple-gray-light);
  box-shadow: var(--apple-shadow);
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .detail-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1d1d1f;
      line-height: 1.4;
    }
  }

  .header-right {
    :deep(.close-btn) {
      padding: 12rpx;
      border-radius: 12rpx;
      transition: all 0.2s ease;

      &:active {
        background: rgba(142, 142, 147, 0.1);
        transform: scale(0.95);
      }
    }
  }
}

.detail-content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.info-section {
  background: var(--apple-surface);
  border-radius: var(--apple-radius);
  border: 1rpx solid var(--apple-gray-light);
  overflow: hidden;
  box-shadow: var(--apple-shadow);

  .section-title {
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, rgba(52, 199, 89, 0.03) 100%);
    padding: 16rpx 24rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    border-bottom: 1rpx solid var(--apple-gray-light);

    text {
      font-size: 26rpx;
      font-weight: 600;
      color: #1d1d1f;
    }
  }

  .info-grid {
    padding: 16rpx 24rpx;
    display: grid;
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8rpx 0;

    .info-label {
      font-size: 24rpx;
      color: var(--apple-gray-dark);
      font-weight: 500;
      min-width: 120rpx;
    }

    .info-value {
      font-size: 24rpx;
      color: #1d1d1f;
      flex: 1;
      text-align: right;
      line-height: 1.3;
    }
  }

  .status-row {
    padding: 16rpx 24rpx;
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }

  .status-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8rpx 0;

    .status-label {
      font-size: 24rpx;
      color: var(--apple-gray-dark);
      font-weight: 500;
      min-width: 120rpx;
    }

    .status-tag {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
      font-weight: 500;
      border: 2rpx solid;

      .status-text {
        font-size: 22rpx;
        font-weight: 500;
      }

      &--enabled {
        background: rgba(52, 199, 89, 0.1);
        border-color: rgba(52, 199, 89, 0.2);
        .status-text {
          color: var(--apple-green);
        }
      }

      &:not(.status-tag--enabled) {
        background: rgba(255, 59, 48, 0.1);
        border-color: rgba(255, 59, 48, 0.2);
        .status-text {
          color: var(--apple-red);
        }
      }

      &--public {
        background: rgba(0, 122, 255, 0.1);
        border-color: rgba(0, 122, 255, 0.2);
        .status-text {
          color: var(--apple-blue);
        }
      }

      &:not(.status-tag--public) {
        background: rgba(255, 149, 0, 0.1);
        border-color: rgba(255, 149, 0, 0.2);
        .status-text {
          color: var(--apple-orange);
        }
      }
    }
  }
}

.detail-footer {
  background: var(--apple-surface);
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--apple-gray-light);
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}
</style>
