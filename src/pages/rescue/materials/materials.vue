<script setup lang="ts">
import { getMaterialHomeApi } from '@/api/modules/rescue/home/<USER>'

// 页面状态
const pageLoaded = ref(false)
const cardsVisible = ref(false)
const loading = ref(false)

// 统计数据
const statistics = ref({
  totalMaterials: 0,
  totalSkus: 0,
  totalOrders: 0,
  warehouseCount: 0,
})

// 获取首页数据
async function fetchHomeData() {
  try {
    loading.value = true
    const response = await getMaterialHomeApi()
    if (response.data) {
      statistics.value = {
        totalMaterials: response.data.productCount || 0,
        totalSkus: response.data.productSkuCount || 0,
        totalOrders: response.data.orderCount || 0,
        warehouseCount: response.data.warehouseCount || 0,
      }
    }
  } catch (error) {
    console.error('获取首页数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 功能入口配置 - 使用Apple设计系统颜色
const functionEntries = ref([
  {
    id: 'application',
    title: '物资申请',
    subtitle: '提交物资需求申请',
    icon: 'add-circle',
    color: '#007AFF',
    gradient: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
    route: '/pages/materials/application',
    badge: null,
  },
  {
    id: 'manage',
    title: '物资管理',
    subtitle: '管理物资管理信息',
    icon: 'layers',
    color: '#34C759',
    gradient: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
    route: '/pages/rescue/materials/manage',
    badge: null,
  },
  {
    id: 'warehouse',
    title: '仓库管理',
    subtitle: '库存查看与管理',
    icon: 'home',
    color: '#FF9500',
    gradient: 'linear-gradient(135deg, #FF9500 0%, #FFCC02 100%)',
    route: '/pages/rescue/materials/warehouse',
    badge: null,
  },
  {
    id: 'review',
    title: '审批管理',
    subtitle: '审核物资申请',
    icon: 'check-circle',
    color: '#FF3B30',
    gradient: 'linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%)',
    route: '/pages/rescue/materials/review',
    badge: null,
  },
  {
    id: 'myApplications',
    title: '我的申请',
    subtitle: '查看申请状态',
    icon: 'user',
    color: '#AF52DE',
    gradient: 'linear-gradient(135deg, #AF52DE 0%, #FF2D92 100%)',
    route: '/pages/materials/my-applications',
    badge: null,
  },
])

// 导航到功能页面
function navigateToFunction(entry: any) {
  uni.vibrateShort()

  // 根据功能ID进行不同的导航
  switch (entry.id) {
    case 'application':
      // 导航到物资申请页面
      uni.navigateTo({
        url: '/pages/rescue/materials/apply',
      })
      break
    case 'myApplications':
      // 导航到我的申请页面
      uni.navigateTo({
        url: '/pages/rescue/materials/my-apply',
      })
      break
    case 'manage':
      // 导航到物资管理页面
      uni.navigateTo({
        url: '/pages/rescue/materials/manage',
      })
      break
    case 'warehouse':
      // 导航到仓库管理页面
      uni.navigateTo({
        url: '/pages/rescue/materials/warehouse',
      })
      break
    case 'review':
      // 导航到审批管理页面
      uni.navigateTo({
        url: '/pages/rescue/materials/review',
      })
      break
    default:
      // 其他功能暂时显示开发中提示
      uni.showToast({
        title: `${entry.title}功能开发中`,
        icon: 'none',
        duration: 2000,
      })
      break
  }
}

// 页面加载动画
function startPageAnimation() {
  pageLoaded.value = true
  setTimeout(() => {
    cardsVisible.value = true
  }, 400)
}

onMounted(() => {
  startPageAnimation()
  fetchHomeData()
})
</script>

<template>
  <view class="materials-page" :class="{ loaded: pageLoaded }">
    <!-- 毛玻璃背景 -->
    <view class="page-background">
      <view class="bg-blur" />
      <view class="bg-gradient" />
    </view>

    <!-- 统计卡片 -->
    <view class="statistics-section" :class="{ isVisible: cardsVisible }">
      <view class="section-header">
        <view class="section-title">数据概览</view>
        <view class="section-subtitle">Material Statistics Overview</view>
      </view>
      <view class="stats-container">
        <view class="stat-card primary">
          <view class="card-background">
            <view class="card-blur" />
          </view>
          <view class="stat-content">
            <view class="stat-icon">
              <wd-icon name="package" size="48rpx" color="#007AFF" />
            </view>
            <view class="stat-info">
              <view class="stat-number">
                {{ loading ? '...' : statistics.totalMaterials }}
              </view>
              <view class="stat-label">总物资数量</view>
            </view>
          </view>
        </view>

        <view class="stat-card secondary">
          <view class="card-background">
            <view class="card-blur" />
          </view>
          <view class="stat-content">
            <view class="stat-icon warning">
              <wd-icon name="clock" size="48rpx" color="#FF9500" />
            </view>
            <view class="stat-info">
              <view class="stat-number">
                {{ loading ? '...' : statistics.totalOrders }}
              </view>
              <view class="stat-label">物资申请总数</view>
            </view>
          </view>
        </view>

        <view class="stat-card tertiary">
          <view class="card-background">
            <view class="card-blur" />
          </view>
          <view class="stat-content">
            <view class="stat-icon success">
              <wd-icon name="layers" size="48rpx" color="#34C759" />
            </view>
            <view class="stat-info">
              <view class="stat-number">
                {{ loading ? '...' : statistics.totalSkus }}
              </view>
              <view class="stat-label">物资规格总数</view>
            </view>
          </view>
        </view>

        <view class="stat-card quaternary">
          <view class="card-background">
            <view class="card-blur" />
          </view>
          <view class="stat-content">
            <view class="stat-icon info">
              <wd-icon name="home" size="48rpx" color="#AF52DE" />
            </view>
            <view class="stat-info">
              <view class="stat-number">
                {{ loading ? '...' : statistics.warehouseCount }}
              </view>
              <view class="stat-label">仓库总数</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要功能入口 -->
    <view class="functions-section" :class="{ isVisible: cardsVisible }">
      <view class="section-header">
        <view class="section-title">主要功能</view>
        <view class="section-subtitle">快速访问常用功能</view>
      </view>

      <view class="functions-grid">
        <view
          v-for="(entry, index) in functionEntries"
          :key="entry.id"
          class="function-card"
          :class="`card-${entry.id}`"
          :style="{ 'animation-delay': `${index * 0.1}s` }"
          @tap="navigateToFunction(entry)"
        >
          <view class="card-background">
            <view class="card-gradient" :class="`gradient-${entry.id}`" />
            <view class="card-overlay" />
          </view>

          <view class="card-content">
            <view class="card-header">
              <view class="card-icon-wrapper">
                <view class="card-icon">
                  <wd-icon :name="entry.icon" size="40rpx" color="#ffffff" />
                </view>
              </view>
              <view v-if="entry.badge" class="card-badge">
                <view class="badge-text">
                  {{ entry.badge }}
                </view>
              </view>
            </view>

            <view class="card-body">
              <view class="card-title">
                {{ entry.title }}
              </view>
              <view class="card-subtitle">
                {{ entry.subtitle }}
              </view>
            </view>

            <view class="card-footer">
              <view class="access-indicator">
                <wd-icon name="arrow-right" size="28rpx" color="rgba(255,255,255,0.8)" />
              </view>
            </view>
          </view>

          <view class="card-shine" />
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" />
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统颜色
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-purple: #af52de;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray4: #d1d1d6;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$background-tertiary: #ffffff;

// 分割线和边框
$separator: rgba(60, 60, 67, 0.36);
$separator-opaque: #c6c6c8;

// 页面容器
.materials-page {
  min-height: 100vh;
  background: $background-secondary;
  position: relative;
  overflow: hidden;
}

// 背景效果
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;

  .bg-blur {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.05) 0%, transparent 70%);
    animation: breathe 15s infinite ease-in-out;
  }

  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(242, 242, 247, 0.4) 50%,
      transparent 100%
    );
    backdrop-filter: blur(40rpx);
  }
}

// 统计区域
.statistics-section {
  padding: 120rpx 40rpx 32rpx;
  position: relative;
  z-index: 5;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s;

  &.isVisible {
    opacity: 1;
    transform: translateY(0);
  }

  .section-header {
    margin-bottom: 32rpx;

    .section-title {
      display: block;
      font-size: 36rpx;
      font-weight: 700;
      color: $label-primary;
      letter-spacing: -0.8rpx;
      margin-bottom: 6rpx;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: $label-secondary;
      font-weight: 500;
      letter-spacing: 0.2rpx;
    }
  }

  .stats-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
  }

  .stat-card {
    flex: 1;
    position: relative;
    border-radius: 20rpx;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1rpx solid rgba(255, 255, 255, 0.6);

    &:active {
      transform: scale(0.96);
    }

    .card-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .card-blur {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.75);
        backdrop-filter: blur(40rpx);
        -webkit-backdrop-filter: blur(40rpx);
      }
    }

    .stat-content {
      position: relative;
      z-index: 2;
      padding: 28rpx;
      display: flex;
      align-items: center;
      gap: 20rpx;
    }

    .stat-icon {
      width: 56rpx;
      height: 56rpx;
      background: rgba(0, 122, 255, 0.1);
      border-radius: 14rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &.warning {
        background: rgba(255, 149, 0, 0.1);
      }

      &.success {
        background: rgba(52, 199, 89, 0.1);
      }

      &.info {
        background: rgba(175, 82, 222, 0.1);
      }
    }

    .stat-info {
      .stat-number {
        display: block;
        font-size: 42rpx;
        font-weight: 700;
        color: $label-primary;
        line-height: 1.1;
        margin-bottom: 4rpx;
        letter-spacing: -1rpx;
      }

      .stat-label {
        font-size: 22rpx;
        color: $label-secondary;
        font-weight: 500;
      }
    }
  }
}

// 功能区域
.functions-section {
  padding: 32rpx 40rpx;
  position: relative;
  z-index: 5;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1) 0.5s;

  &.isVisible {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  margin-bottom: 32rpx;

  .section-title {
    display: block;
    font-size: 36rpx;
    font-weight: 700;
    color: $label-primary;
    letter-spacing: -0.8rpx;
    margin-bottom: 6rpx;
  }

  .section-subtitle {
    font-size: 24rpx;
    color: $label-secondary;
    font-weight: 500;
    letter-spacing: 0.2rpx;
  }
}

.functions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.function-card {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20rpx);
  animation: slideInUp 1s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 2rpx 12rpx rgba(0, 0, 0, 0.08),
    0 1rpx 2rpx rgba(0, 0, 0, 0.06),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.15);

  &:active {
    transform: scale(0.96);
    box-shadow:
      0 4rpx 24rpx rgba(0, 0, 0, 0.12),
      0 2rpx 4rpx rgba(0, 0, 0, 0.08),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.25);
  }

  .card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .card-gradient {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }

    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 100%
      );
      backdrop-filter: blur(40rpx);
      -webkit-backdrop-filter: blur(40rpx);
    }
  }

  .card-content {
    position: relative;
    z-index: 3;
    padding: 16rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8rpx;
  }

  .card-icon-wrapper {
    .card-icon {
      width: 40rpx;
      height: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(16rpx);
      -webkit-backdrop-filter: blur(16rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.25);
      box-shadow:
        0 1rpx 4rpx rgba(0, 0, 0, 0.1),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
    }
  }

  .card-badge {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    border-radius: 12rpx;
    padding: 4rpx 10rpx;
    min-width: 24rpx;
    height: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);

    .badge-text {
      font-size: 18rpx;
      font-weight: 700;
      color: $system-red;
    }
  }

  .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .card-title {
      display: block;
      font-size: 24rpx;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 3rpx;
      letter-spacing: -0.4rpx;
      line-height: 1.2;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
    }

    .card-subtitle {
      font-size: 16rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 400;
      line-height: 1.3;
      letter-spacing: 0.2rpx;
    }
  }

  .card-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .access-indicator {
      width: 24rpx;
      height: 24rpx;
      background: rgba(255, 255, 255, 0.15);
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(8rpx);
      -webkit-backdrop-filter: blur(8rpx);
      border: 2rpx solid rgba(255, 255, 255, 0.2);
    }
  }

  .card-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    transform: rotate(45deg);
    animation: shine 3s infinite;
    z-index: 4;
    pointer-events: none;
  }

  // 特定卡片渐变
  &.card-application .card-gradient {
    background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  }

  &.card-manage .card-gradient {
    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
  }

  &.card-warehouse .card-gradient {
    background: linear-gradient(135deg, #ff9500 0%, #ffcc02 100%);
  }

  &.card-review .card-gradient {
    background: linear-gradient(135deg, #ff3b30 0%, #ff6b6b 100%);
  }

  &.card-myApplications .card-gradient {
    background: linear-gradient(135deg, #af52de 0%, #ff2d92 100%);
  }
}

// 安全区域
.safe-area-bottom {
  height: 120rpx;
  position: relative;
  z-index: 5;
}

// 动画定义
@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes shine {
  0% {
    transform: rotate(45deg) translateX(-200%);
  }
  50% {
    transform: rotate(45deg) translateX(0%);
  }
  100% {
    transform: rotate(45deg) translateX(200%);
  }
}
</style>
