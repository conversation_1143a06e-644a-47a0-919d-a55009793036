<script setup lang="ts">
import type { IOrder } from '@/api/interface/rescue/order/order'
import { getMyApplyListApi } from '@/api/modules/rescue/order/order'
import OrderCard from '@/components/rescue/order/OrderCard.vue'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
// 页面状态
const pageLoaded = ref(false)
const cardsVisible = ref(false)
const loading = ref(false)
const refreshing = ref(false)

// 申请列表数据
const applyList = ref<IOrder.OrderVO[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)

// 筛选参数
const filterParams = ref({
  page: 1,
  limit: 10,
  statusList: [] as string[],
  searchType: 'workNumber',
  searchText: '',
  workNumber: '',
  orderNumber: '',
})

// 详情弹窗状态
const showDetail = ref(false)
const selectedOrderId = ref<number | undefined>(undefined)

// 计算属性
const filteredList = computed(() => {
  return applyList.value.filter((item) => {
    // 状态筛选：如果有选择具体状态，则进行筛选；如果是空数组（全部状态），则不筛选
    if (
      filterParams.value.statusList.length > 0 &&
      !filterParams.value.statusList.includes(item.status)
    ) {
      return false
    }
    if (filterParams.value.workNumber && !item.workNumber.includes(filterParams.value.workNumber)) {
      return false
    }
    if (
      filterParams.value.orderNumber &&
      !item.orderNumber.includes(filterParams.value.orderNumber)
    ) {
      return false
    }
    return true
  })
})

// 获取申请列表
async function getApplyList(loadMore = false) {
  try {
    loading.value = true

    const params = {
      ...filterParams.value,
      page: loadMore ? currentPage.value : 1,
      limit: pageSize.value,
    }

    const result = await getMyApplyListApi(params)

    if (loadMore) {
      applyList.value = [...applyList.value, ...(result.data.rows || [])]
    } else {
      applyList.value = result.data.rows || []
    }

    total.value = result.data.total || 0
    hasMore.value = applyList.value.length < total.value

    if (loadMore) {
      currentPage.value++
    }
  } catch (error) {
    console.error('获取申请列表失败:', error)
    uni.showToast({
      title: '获取申请列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 查看申请详情
function viewDetail(item: IOrder.OrderVO) {
  selectedOrderId.value = item.id
  showDetail.value = true
}

// 下拉刷新
function onRefresh() {
  refreshing.value = true
  currentPage.value = 1
  getApplyList(false)
}

// 加载更多
function onLoadMore() {
  if (hasMore.value && !loading.value) {
    getApplyList(true)
  }
}

// 筛选状态变化
function onStatusChange(statusList: string[]) {
  filterParams.value.statusList = statusList
  currentPage.value = 1
  getApplyList(false)
}

// 搜索
function onSearch() {
  console.log('搜索')
  currentPage.value = 1
  getApplyList(false)
}

// 重置筛选条件
function resetFilters() {
  filterParams.value = {
    page: 1,
    limit: 10,
    statusList: [],
    searchType: 'workNumber',
    searchText: '',
    workNumber: '',
    orderNumber: '',
  }
  currentPage.value = 1
  getApplyList(false)
}

// 页面加载动画
function startPageAnimation() {
  pageLoaded.value = true
  setTimeout(() => {
    cardsVisible.value = true
  }, 400)
}

onMounted(() => {
  startPageAnimation()
  getApplyList(false)
})
</script>

<template>
  <view class="my-apply-page" :class="{ loaded: pageLoaded }">
    <!-- 毛玻璃背景 -->
    <view class="page-background">
      <view class="bg-blur" />
      <view class="bg-gradient" />
    </view>

    <!-- 页面头部 -->
    <view class="page-header" :class="{ isVisible: pageLoaded }">
      <view class="header-content">
        <view class="title-section">
          <view class="page-title">我的申请</view>
          <view class="page-subtitle">My Applications</view>
        </view>
        <view class="header-actions">
          <view class="refresh-btn" @tap="onRefresh">
            <wd-icon name="refresh" size="44rpx" color="#1D1D1F" />
          </view>
        </view>
      </view>
    </view>

    <!-- 筛选器 -->
    <view class="filter-section" :class="{ isVisible: cardsVisible }">
      <OrderFilter
        v-model="filterParams"
        :show-status="true"
        :show-search="true"
        :default-expanded="true"
        @search="onSearch"
        @reset="resetFilters"
        @status-change="onStatusChange"
      />
    </view>

    <!-- 申请列表 -->
    <view class="apply-list-section" :class="{ isVisible: cardsVisible }">
      <view v-if="loading && applyList.length === 0" class="loading-container">
        <wd-loading />
        <view class="loading-text">加载中...</view>
      </view>

      <view v-else-if="filteredList.length === 0" class="empty-container">
        <wd-icon name="inbox" size="128rpx" color="#C7C7CC" />
        <view class="empty-text">暂无申请记录</view>
      </view>

      <view v-else class="apply-list">
        <OrderCard
          v-for="(item, index) in filteredList"
          :key="item.id"
          :order="item"
          :show-reviewer="true"
          class="apply-item"
          :style="{ 'animation-delay': `${index * 0.1}s` }"
          @click="viewDetail"
        />
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && applyList.length > 0" class="load-more-container">
        <wd-button type="primary" size="large" :loading="loading" @tap="onLoadMore">
          {{ loading ? '加载中...' : '加载更多' }}
        </wd-button>
      </view>
    </view>

    <!-- 详情弹窗 -->
    <OrderDetail v-model="showDetail" :order-id="selectedOrderId" />

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom" />
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统颜色
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-purple: #af52de;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray4: #d1d1d6;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$background-tertiary: #ffffff;

// 分割线和边框
$separator: rgba(60, 60, 67, 0.36);
$separator-opaque: #c6c6c8;

// 页面容器
:deep(.my-apply-page) {
  min-height: 100vh;
  background: $background-secondary;
  position: relative;
  overflow: hidden;
}

// 背景效果
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;

  .bg-blur {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(ellipse at center, rgba(0, 122, 255, 0.05) 0%, transparent 70%);
    animation: breathe 15s infinite ease-in-out;
  }

  .bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(242, 242, 247, 0.4) 50%,
      transparent 100%
    );
    // backdrop-filter: blur(40rpx);
  }
}

// 页面头部
.page-header {
  background: rgba(255, 255, 255, 0.85);
  // backdrop-filter: blur(40rpx);
  // -webkit-backdrop-filter: blur(40rpx);
  padding: 88rpx 40rpx 36rpx;
  border-bottom: 1rpx solid $separator;
  position: relative;
  // z-index: 10;
  opacity: 0;
  transform: translateY(-30rpx);
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);

  &.isVisible {
    opacity: 1;
    transform: translateY(0);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }

  .title-section {
    .page-title {
      display: block;
      font-size: 54rpx;
      font-weight: 700;
      color: $label-primary;
      line-height: 1.1;
      margin-bottom: 6rpx;
      letter-spacing: -1rpx;
    }

    .page-subtitle {
      font-size: 26rpx;
      color: $label-secondary;
      font-weight: 500;
      letter-spacing: 0.4rpx;
    }
  }

  .header-actions {
    .refresh-btn {
      width: 72rpx;
      height: 72rpx;
      background: rgba(0, 122, 255, 0.08);
      border-radius: 18rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 1rpx solid rgba(0, 122, 255, 0.1);

      &:active {
        transform: scale(0.92);
        background: rgba(0, 122, 255, 0.15);
      }
    }
  }
}

// 筛选器
:deep(.filter-section) {
  padding: 32rpx 40rpx;
  position: relative;
}

// 申请列表
.apply-list-section {
  padding: 32rpx 40rpx;
  position: relative;
  opacity: 0;
  transform: translateY(30rpx);
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1) 0.5s;

  &.isVisible {
    opacity: 1;
    transform: translateY(0);
  }

  .loading-container,
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80rpx 20rpx;

    .loading-text,
    .empty-text {
      font-size: 28rpx;
      color: $label-secondary;
      margin-top: 16rpx;
    }
  }

  .apply-list {
    .apply-item {
      margin-bottom: 24rpx;
      opacity: 0;
      // transform: translateY(20rpx);
      animation: slideInUp 0.8s cubic-bezier(0.23, 1, 0.32, 1) forwards;
    }
  }

  .load-more-container {
    padding: 32rpx 0;
    display: flex;
    justify-content: center;
  }
}

// 安全区域
.safe-area-bottom {
  height: 120rpx;
  position: relative;
  z-index: 5;
}

// 动画定义
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1) rotate(180deg);
    opacity: 0.6;
  }
}
</style>
