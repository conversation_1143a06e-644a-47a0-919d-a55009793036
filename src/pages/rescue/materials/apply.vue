<script setup lang="ts">
import type { IOrder } from '@/api/interface/rescue/order/order'
import type { IProduct } from '@/api/interface/rescue/product/product'
import { createOrderApi } from '@/api/modules/rescue/order/order'
import { getProductSelectApi } from '@/api/modules/rescue/product/product'

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
})
// 组件会通过全局注册或者自动导入的方式使用，无需显式导入

// 页面状态
const loading = ref(false)
const loadingMore = ref(false)
const submitting = ref(false)
const hasMore = ref(true)
const currentStep = ref(0)
const showProductDetail = ref(false)

const showSelectedItems = ref(false)

// 新增：删除确认弹窗状态
const showDeleteConfirm = ref(false)
const deleteItemIndex = ref(-1)

// 新增：数量编辑状态
const editingQuantity = ref(false)
const editingItemIndex = ref(-1)
const tempQuantity = ref(1)

// 步骤配置
const steps = [
  { title: '选择物资', subtitle: '筛选和选择需要的物资' },
  { title: '确认数量', subtitle: '确认物资数量和规格' },
  { title: '填写信息', subtitle: '完善订单信息并提交' },
]

// 筛选参数
const filterParams = ref({
  warehouseId: undefined as number | undefined,
  tagIds: [] as number[],
  searchType: 'name' as string,
  searchText: '',
  productName: '',
  productNumber: '',
})

// 分页参数
const pageParams = ref({
  page: 1,
  limit: 10,
})

// 数据状态
const productList = ref<IProduct.ProductSelectVO[]>([])
const selectedProduct = ref<IProduct.ProductSelectVO | null>(null)
const cartItems = ref<IProduct.CartItem[]>([])

// 订单表单
const orderForm = ref<IOrder.OrderFormData>({
  workNumber: '',
  reason: '',
  remark: '',
})

// 计算属性
const totalQuantity = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + item.quantity, 0)
})

const totalAmount = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + item.totalPrice, 0).toFixed(2)
})

// 事件处理（现在由 MaterialsFilter 组件处理）

function handleSearch() {
  pageParams.value.page = 1
  hasMore.value = true
  productList.value = []
  loadProducts()
}

function resetFilter() {
  filterParams.value = {
    warehouseId: undefined,
    tagIds: [],
    searchType: 'name',
    searchText: '',
    productName: '',
    productNumber: '',
  }
  handleSearch()
}

function handleProductClick(product: IProduct.ProductSelectVO) {
  selectedProduct.value = product
  showProductDetail.value = true
}

function addToCart(sku: IProduct.ProductSku) {
  if (sku.stock === 0) {
    uni.showToast({
      title: '该规格缺货',
      icon: 'none',
    })
    return
  }

  const existingIndex = cartItems.value.findIndex((item) => item.skuId === sku.id)

  if (existingIndex >= 0) {
    // 已存在，增加数量
    const existingItem = cartItems.value[existingIndex]
    if (existingItem.quantity < sku.stock) {
      existingItem.quantity += 1
      existingItem.totalPrice = existingItem.price * existingItem.quantity
    } else {
      uni.showToast({
        title: '库存不足',
        icon: 'none',
      })
      return
    }
  } else {
    // 新增
    if (!selectedProduct.value) return

    const cartItem: IProduct.CartItem = {
      skuId: sku.id,
      skuName: sku.name,
      productId: selectedProduct.value.id,
      productName: selectedProduct.value.name,
      productNumber: selectedProduct.value.productNumber,
      image: sku.image || selectedProduct.value.image,
      price: sku.price,
      quantity: 1,
      stock: sku.stock,
      totalPrice: sku.price,
    }
    cartItems.value.push(cartItem)
  }

  uni.showToast({
    title: '已添加到购物车',
    icon: 'success',
  })
}

function updateQuantity(index: number, newQuantity: number) {
  if (newQuantity < 1) {
    removeCartItem(index)
    return
  }

  const item = cartItems.value[index]
  if (newQuantity > item.stock) {
    uni.showToast({
      title: '库存不足',
      icon: 'none',
    })
    return
  }

  item.quantity = newQuantity
  item.totalPrice = item.price * newQuantity
}

function removeCartItem(index: number) {
  cartItems.value.splice(index, 1)
}

// 新增：显示删除确认弹窗
function showDeleteConfirmDialog(index: number) {
  deleteItemIndex.value = index
  showDeleteConfirm.value = true
}

// 新增：确认删除物资
function confirmDeleteItem() {
  if (deleteItemIndex.value >= 0) {
    cartItems.value.splice(deleteItemIndex.value, 1)
    showDeleteConfirm.value = false
    deleteItemIndex.value = -1

    uni.showToast({
      title: '已删除物资',
      icon: 'success',
    })
  }
}

// 新增：取消删除
function cancelDeleteItem() {
  showDeleteConfirm.value = false
  deleteItemIndex.value = -1
}

// 新增：开始编辑数量
function startEditQuantity(index: number) {
  editingItemIndex.value = index
  tempQuantity.value = cartItems.value[index].quantity
  editingQuantity.value = true

  // 延迟获取焦点和选中文本
  setTimeout(() => {
    const input = document.querySelector('.quantity-input input') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  }, 100)
}

// 新增：确认修改数量
function confirmEditQuantity() {
  if (editingItemIndex.value >= 0 && tempQuantity.value > 0) {
    const item = cartItems.value[editingItemIndex.value]

    if (tempQuantity.value > item.stock) {
      uni.showToast({
        title: '库存不足',
        icon: 'none',
      })
      return
    }

    item.quantity = tempQuantity.value
    item.totalPrice = item.price * tempQuantity.value

    editingQuantity.value = false
    editingItemIndex.value = -1

    uni.showToast({
      title: '数量已更新',
      icon: 'success',
    })
  }
}

// 新增：取消编辑数量
function cancelEditQuantity() {
  editingQuantity.value = false
  editingItemIndex.value = -1
  tempQuantity.value = 1
}

// 新增：验证数量输入
function validateQuantity() {
  // 确保数量为正整数
  if (tempQuantity.value < 1) {
    tempQuantity.value = 1
  }

  // 确保数量不超过库存
  if (editingItemIndex.value >= 0) {
    const item = cartItems.value[editingItemIndex.value]
    if (tempQuantity.value > item.stock) {
      // 在这里不自动限制数量，只是提示用户
      // 让用户自己决定是否要修改
    }
  }
}

function goToStep(step: number) {
  // 关闭所有弹窗
  showProductDetail.value = false
  showSelectedItems.value = false
  showDeleteConfirm.value = false
  editingQuantity.value = false

  currentStep.value = step
}

async function loadProducts() {
  if (loading.value) return

  loading.value = true
  try {
    const params: IProduct.ProductListDTO = {
      page: pageParams.value.page,
      limit: pageParams.value.limit,
    }

    if (filterParams.value.warehouseId) {
      params.warehouseId = filterParams.value.warehouseId
    }
    if (filterParams.value.tagIds.length > 0) {
      params.tagIds = filterParams.value.tagIds
    }
    if (filterParams.value.productName) {
      params.name = filterParams.value.productName
    }
    if (filterParams.value.productNumber) {
      params.productNumber = filterParams.value.productNumber
    }

    const res = await getProductSelectApi(params)

    if (pageParams.value.page === 1) {
      productList.value = res.data.rows
    } else {
      productList.value.push(...res.data.rows)
    }

    hasMore.value = productList.value.length < res.data.total
  } catch (error) {
    console.error('加载产品失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

async function loadMoreProducts() {
  if (loadingMore.value || !hasMore.value) return

  loadingMore.value = true
  pageParams.value.page++
  await loadProducts()
  loadingMore.value = false
}

async function submitOrder() {
  if (!orderForm.value.reason) {
    uni.showToast({
      title: '请输入操作原因',
      icon: 'none',
    })
    return
  }

  if (cartItems.value.length === 0) {
    uni.showToast({
      title: '请选择物资',
      icon: 'none',
    })
    return
  }

  submitting.value = true
  try {
    const orderData: IOrder.OrderCreateDTO = {
      workNumber: orderForm.value.workNumber,
      type: 'APPLY',
      reason: orderForm.value.reason,
      remark: orderForm.value.remark,
      skuQuantityList: cartItems.value.map((item) => ({
        skuId: item.skuId,
        quantity: item.quantity,
      })),
    }

    await createOrderApi(orderData)

    uni.showToast({
      title: '申请提交成功',
      icon: 'success',
    })

    // 延迟返回物资页面
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('提交订单失败:', error)
    uni.showToast({
      title: '提交失败',
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

onLoad(() => {
  loadProducts()
})
</script>

<template>
  <view class="material-apply-page">
    <!-- 步骤导航 -->
    <view class="step-navigator">
      <view class="step-container">
        <view
          v-for="(step, index) in steps"
          :key="index"
          class="step-item"
          :class="{ active: currentStep === index, completed: index < currentStep }"
        >
          <view class="step-circle">
            <view v-if="index < currentStep" class="step-check">✓</view>
            <view v-else class="step-number">
              {{ index + 1 }}
            </view>
          </view>
          <view class="step-title">
            {{ step.title }}
          </view>
        </view>
      </view>
    </view>

    <!-- 步骤内容 -->
    <view class="step-content">
      <!-- 第一步：物资选择 -->
      <view v-if="currentStep === 0" class="step-panel">
        <view class="panel-header">
          <view class="panel-title">选择物资</view>
          <view class="panel-subtitle">通过筛选条件查找并选择需要的物资</view>
        </view>
        <view class="mb-40rpx">
          <!-- 筛选条件 -->
          <MaterialsFilter
            v-model="filterParams"
            :default-expanded="true"
            :z-index="999"
            @search="handleSearch"
            @reset="resetFilter"
          />
        </view>
        <!-- 物资列表 -->
        <view class="product-list">
          <view v-if="loading" class="loading-container">
            <wd-loading size="48rpx" />
            <view class="loading-text">加载中...</view>
          </view>

          <view v-else-if="productList.length === 0" class="empty-state">
            <wd-icon name="package" size="96rpx" color="#C7C7CC" />
            <view class="empty-text">暂无物资数据</view>
            <view class="empty-subtitle">请尝试调整筛选条件</view>
          </view>

          <view v-else class="product-list-container">
            <view
              v-for="product in productList"
              :key="product.id"
              class="product-card"
              @click="handleProductClick(product)"
            >
              <view class="product-image">
                <image
                  v-if="product.image"
                  :src="product.image"
                  mode="aspectFill"
                  class="product-img"
                />
                <view v-else class="product-placeholder">
                  <wd-icon name="package" size="64rpx" color="#C7C7CC" />
                </view>
              </view>
              <view class="product-info">
                <view class="product-name">
                  {{ product.name }}
                </view>
                <view class="product-number">编号: {{ product.productNumber }}</view>
                <view class="product-status">
                  <view class="status-text">{{ product.productSkus.length }} 个规格可选</view>
                  <wd-icon name="arrow-right" size="28rpx" color="#8E8E93" />
                </view>
              </view>
            </view>
          </view>

          <!-- 加载更多 -->
          <view v-if="hasMore" class="load-more-container">
            <view
              class="load-more-button"
              :class="{ loading: loadingMore }"
              @click="loadMoreProducts"
            >
              <wd-loading v-if="loadingMore" size="32rpx" color="#007AFF" />
              <wd-icon v-else name="arrow-down" size="32rpx" color="#007AFF" />
              <view class="load-more-text">
                {{ loadingMore ? '加载中...' : '点击加载更多' }}
              </view>
            </view>
          </view>
        </view>

        <!-- 已选物资栏 -->
        <view class="selected-items-bar">
          <view class="selected-items-left" @click="showSelectedItems = true">
            <wd-icon name="shopping-cart" size="40rpx" color="#007AFF" />
            <view class="selected-items-text">
              {{ cartItems.length === 0 ? '未选物资' : `已选 ${cartItems.length} 件` }}
            </view>
            <view v-if="cartItems.length > 0" class="selected-items-badge">
              {{ cartItems.length }}
            </view>
          </view>
          <wd-button
            type="primary"
            size="large"
            custom-class="next-step-button"
            :disabled="cartItems.length === 0"
            @click="goToStep(1)"
          >
            下一步
          </wd-button>
        </view>
      </view>

      <!-- 第二步：购物车确认 -->
      <view v-if="currentStep === 1" class="step-panel">
        <view class="panel-header">
          <view class="panel-title">确认物资</view>
          <view class="panel-subtitle">确认选择的物资数量和规格</view>
        </view>

        <view class="cart-list">
          <view v-if="cartItems.length === 0" class="empty-cart">
            <wd-icon name="shopping-cart" size="96rpx" color="#C7C7CC" />
            <view class="empty-text">购物车为空</view>
            <wd-button type="text" @click="goToStep(0)">去选择物资</wd-button>
          </view>

          <view v-else>
            <view v-for="(item, index) in cartItems" :key="item.skuId" class="cart-item">
              <view class="cart-item-image">
                <image v-if="item.image" :src="item.image" mode="aspectFill" class="item-img" />
                <view v-else class="item-placeholder">
                  <wd-icon name="package" size="48rpx" color="#C7C7CC" />
                </view>
              </view>
              <view class="cart-item-info">
                <view class="item-name">
                  {{ item.productName }}
                </view>
                <view class="item-sku">
                  {{ item.skuName }}
                </view>
                <view class="item-number-row">
                  <view class="item-number">
                    {{ item.productNumber }}
                  </view>
                </view>
                <view class="item-price">
                  <view class="price-label">单价:</view>
                  <view class="price-value">¥{{ item.price }}</view>
                </view>
              </view>
              <view class="cart-item-actions">
                <view class="quantity-controls">
                  <wd-button
                    type="text"
                    size="small"
                    custom-class="quantity-btn"
                    @click="updateQuantity(index, item.quantity - 1)"
                  >
                    -
                  </wd-button>
                  <view class="quantity-display" @click="startEditQuantity(index)">
                    <view class="quantity-text">
                      {{ item.quantity }}
                    </view>
                  </view>
                  <wd-button
                    type="text"
                    size="small"
                    custom-class="quantity-btn"
                    @click="updateQuantity(index, item.quantity + 1)"
                  >
                    +
                  </wd-button>
                </view>
                <view class="item-total">
                  <view class="total-label">小计:</view>
                  <view class="total-value">¥{{ item.totalPrice }}</view>
                </view>
                <wd-button
                  type="text"
                  size="small"
                  icon="delete"
                  custom-class="remove-btn"
                  @click="showDeleteConfirmDialog(index)"
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 总计 -->
        <view v-if="cartItems.length > 0" class="cart-summary">
          <view class="summary-row">
            <view class="summary-label">总计数量:</view>
            <view class="summary-value">{{ totalQuantity }} 件</view>
          </view>
          <view class="summary-row">
            <view class="summary-label">总计金额:</view>
            <view class="summary-value total-amount">¥{{ totalAmount }}</view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="step-actions">
          <wd-button type="default" size="large" custom-class="step-button" @click="goToStep(0)">
            返回选择
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            custom-class="step-button"
            :disabled="cartItems.length === 0"
            @click="goToStep(2)"
          >
            下一步
          </wd-button>
        </view>
      </view>

      <!-- 第三步：订单信息 -->
      <view v-if="currentStep === 2" class="step-panel">
        <view class="panel-header">
          <view class="panel-title">填写订单信息</view>
          <view class="panel-subtitle">完善订单信息并提交申请</view>
        </view>

        <!-- 已选物资概览 -->
        <view class="selected-summary">
          <view class="summary-header">
            <view class="summary-title">已选物资</view>
            <view class="summary-count">{{ cartItems.length }} 类物资</view>
          </view>
          <view class="summary-items">
            <view v-for="item in cartItems" :key="item.skuId" class="summary-item">
              <view class="summary-item-name">
                {{ item.productName }}
              </view>
              <view class="summary-item-sku">
                {{ item.skuName }}
              </view>
              <view class="summary-item-quantity">× {{ item.quantity }}</view>
            </view>
          </view>
        </view>

        <!-- 订单表单 -->
        <view class="order-form">
          <view class="form-section">
            <view class="form-item">
              <view class="form-label">任务编号</view>
              <wd-input
                v-model="orderForm.workNumber"
                placeholder="请输入任务编号（可选）"
                clearable
                no-border
              />
            </view>
            <view class="form-item">
              <view class="form-label required">操作原因</view>
              <wd-textarea
                v-model="orderForm.reason"
                placeholder="请输入操作原因"
                :rows="3"
                clearable
              />
            </view>
            <view class="form-item">
              <view class="form-label">备注</view>
              <wd-textarea
                v-model="orderForm.remark"
                placeholder="请输入备注信息（可选）"
                :rows="3"
                clearable
              />
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="step-actions">
          <wd-button type="default" size="large" custom-class="step-button" @click="goToStep(1)">
            上一步
          </wd-button>
          <wd-button
            type="primary"
            size="large"
            custom-class="step-button"
            :loading="submitting"
            :disabled="!orderForm.reason"
            @click="submitOrder"
          >
            {{ submitting ? '提交中...' : '提交申请' }}
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 产品详情弹窗 -->
    <wd-popup
      v-model="showProductDetail"
      position="bottom"
      :close-on-click-modal="true"
      :lock-scroll="true"
      :root-portal="true"
      :z-index="88"
      custom-class="product-detail-popup"
    >
      <view v-if="selectedProduct" class="product-detail">
        <view class="detail-header">
          <view class="detail-title">
            {{ selectedProduct.name }}
          </view>
          <wd-button type="text" icon="close" size="small" @click="showProductDetail = false" />
        </view>

        <view class="detail-content">
          <view class="detail-info">
            <view class="detail-number">物资编号: {{ selectedProduct.productNumber }}</view>
            <view class="detail-image">
              <image
                v-if="selectedProduct.image"
                :src="selectedProduct.image"
                mode="aspectFill"
                class="detail-img"
              />
              <view v-else class="detail-img-placeholder">
                <wd-icon name="package" size="96rpx" color="#C7C7CC" />
                <view class="placeholder-text">暂无图片</view>
              </view>
            </view>
          </view>

          <view class="detail-skus">
            <view class="skus-title">选择规格</view>
            <view class="skus-grid">
              <view
                v-for="sku in selectedProduct.productSkus"
                :key="sku.id"
                class="sku-card"
                :class="{ disabled: sku.stock === 0 }"
                @click="addToCart(sku)"
              >
                <view class="sku-image">
                  <image v-if="sku.image" :src="sku.image" mode="aspectFill" class="sku-img" />
                  <view v-else class="sku-img-placeholder">
                    <wd-icon name="package" size="40rpx" color="#C7C7CC" />
                  </view>
                </view>
                <view class="sku-info">
                  <view class="sku-name">
                    {{ sku.name }}
                  </view>
                  <view class="sku-price">¥{{ sku.price }}</view>
                  <view class="sku-stock">
                    {{ sku.stock > 0 ? `库存: ${sku.stock}` : '缺货' }}
                  </view>
                </view>
                <view v-if="sku.stock > 0" class="sku-action">
                  <wd-icon name="add-circle" size="40rpx" color="#007AFF" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 已选物资弹窗 -->
    <wd-popup
      v-model="showSelectedItems"
      position="bottom"
      :close-on-click-modal="true"
      :lock-scroll="true"
      :root-portal="true"
      custom-class="selected-items-popup"
    >
      <view class="selected-items-detail">
        <view class="detail-header">
          <view class="detail-title">已选物资</view>
          <wd-button type="text" icon="close" size="small" @click="showSelectedItems = false" />
        </view>

        <view class="selected-items-content">
          <view v-if="cartItems.length === 0" class="empty-selected">
            <wd-icon name="shopping-cart" size="96rpx" color="#C7C7CC" />
            <view class="empty-text">还没有选择物资</view>
            <view class="empty-subtitle">请先选择需要的物资</view>
          </view>

          <view v-else class="selected-items-list">
            <view v-for="item in cartItems" :key="item.skuId" class="selected-item">
              <view class="selected-item-image">
                <image v-if="item.image" :src="item.image" mode="aspectFill" class="item-img" />
                <view v-else class="item-placeholder">
                  <wd-icon name="package" size="40rpx" color="#C7C7CC" />
                </view>
              </view>
              <view class="selected-item-info">
                <view class="item-name">
                  {{ item.productName }}
                </view>
                <view class="item-sku">
                  {{ item.skuName }}
                </view>
                <view class="item-quantity">数量: {{ item.quantity }}</view>
              </view>
              <view class="selected-item-price">
                <view class="price-text">¥{{ item.totalPrice }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 删除确认弹窗 -->
    <wd-popup
      v-model="showDeleteConfirm"
      position="center"
      :close-on-click-modal="false"
      :lock-scroll="true"
      :root-portal="true"
      custom-class="delete-confirm-popup"
    >
      <view class="delete-confirm-content">
        <view class="delete-confirm-header">
          <wd-icon name="delete" size="96rpx" color="#FF3B30" />
          <view class="delete-confirm-title">删除物资</view>
          <view v-if="deleteItemIndex >= 0" class="delete-item-info">
            <view class="delete-item-name">
              {{ cartItems[deleteItemIndex]?.productName }}
            </view>
            <view class="delete-item-sku">
              {{ cartItems[deleteItemIndex]?.skuName }}
            </view>
          </view>
          <view class="delete-confirm-text">确定要删除该物资吗？此操作不可撤销。</view>
        </view>
        <view class="delete-confirm-actions">
          <wd-button
            type="text"
            size="large"
            custom-class="delete-confirm-button cancel-button"
            @click="cancelDeleteItem"
          >
            取消
          </wd-button>
          <wd-button
            type="text"
            size="large"
            custom-class="delete-confirm-button confirm-button"
            @click="confirmDeleteItem"
          >
            删除
          </wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- 数量编辑弹窗 -->
    <wd-popup
      v-model="editingQuantity"
      position="center"
      :close-on-click-modal="false"
      :lock-scroll="true"
      :root-portal="true"
      custom-class="quantity-edit-popup"
    >
      <view class="quantity-edit-content">
        <view class="quantity-edit-header">
          <view class="quantity-edit-title">修改数量</view>
          <view class="quantity-edit-subtitle">请输入新的数量</view>
        </view>
        <view class="quantity-edit-input-section">
          <wd-input
            v-model="tempQuantity"
            type="number"
            placeholder="请输入数量"
            class="quantity-input"
            no-border
            :class="{
              'invalid-quantity': tempQuantity > (cartItems[editingItemIndex]?.stock || 0),
            }"
            @confirm="confirmEditQuantity"
            @input="validateQuantity"
          />
          <view v-if="editingItemIndex >= 0" class="stock-hint">
            <view class="stock-text">库存: {{ cartItems[editingItemIndex]?.stock || 0 }}</view>
          </view>
          <view v-if="tempQuantity > (cartItems[editingItemIndex]?.stock || 0)" class="error-hint">
            <view class="error-text">数量不能超过库存</view>
          </view>
        </view>
        <view class="quantity-edit-actions">
          <wd-button
            type="text"
            size="large"
            custom-class="quantity-edit-button cancel-button"
            @click="cancelEditQuantity"
          >
            取消
          </wd-button>
          <wd-button
            type="text"
            size="large"
            custom-class="quantity-edit-button confirm-button"
            :disabled="tempQuantity > (cartItems[editingItemIndex]?.stock || 0) || tempQuantity < 1"
            @click="confirmEditQuantity"
          >
            确定
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
// Apple设计系统变量
$system-blue: #007aff;
$system-green: #34c759;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$system-gray-light: #c7c7cc;
$system-gray-dark: #48484a;
$background-primary: #ffffff;
$background-secondary: #f2f2f7;
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$separator: rgba(60, 60, 67, 0.36);

.material-apply-page {
  min-height: 100vh;
  background: $background-secondary;
}

// 步骤导航
.step-navigator {
  background: $background-primary;
  padding: 40rpx;
  border-bottom: 1rpx solid $separator;

  .step-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 40rpx;
      left: 40rpx;
      right: 40rpx;
      height: 4rpx;
      background: $system-gray-light;
      z-index: 1;
    }

    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;
      z-index: 2;

      &.active {
        .step-circle {
          background: $system-blue;
          border-color: $system-blue;
          color: white;
        }
        .step-title {
          color: $system-blue;
          font-weight: 600;
        }
      }

      &.completed {
        .step-circle {
          background: $system-green;
          border-color: $system-green;
          color: white;
        }
        .step-title {
          color: $system-green;
        }
      }

      .step-circle {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 4rpx solid $system-gray-light;
        background: $background-primary;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: $system-gray;
        transition: all 0.3s ease;

        .step-check {
          font-size: 28rpx;
          font-weight: bold;
        }
      }

      .step-title {
        font-size: 24rpx;
        color: $label-secondary;
        text-align: center;
        transition: all 0.3s ease;
      }
    }
  }
}

// 步骤内容
.step-content {
  flex: 1;
  padding: 40rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100px);
}

.step-panel {
  .panel-header {
    margin-bottom: 48rpx;
    text-align: center;

    .panel-title {
      display: block;
      font-size: 48rpx;
      font-weight: 700;
      color: $label-primary;
      margin-bottom: 16rpx;
    }

    .panel-subtitle {
      font-size: 28rpx;
      color: $label-secondary;
    }
  }
}

// 筛选区域样式已移动到 MaterialsFilter 组件中

// 产品列表
.product-list {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .loading-text {
      margin-top: 24rpx;
      font-size: 28rpx;
      color: $label-secondary;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-text {
      font-size: 32rpx;
      color: $label-secondary;
      margin-top: 32rpx;
    }

    .empty-subtitle {
      font-size: 28rpx;
      color: $label-secondary;
      margin-top: 16rpx;
    }
  }

  .product-list-container {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-bottom: 40rpx;

    .product-card {
      background: $background-primary;
      border-radius: 24rpx;
      padding: 32rpx;
      display: flex;
      gap: 32rpx;
      align-items: center;
      transition: all 0.2s ease;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
      }

      .product-image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
        overflow: hidden;
        flex-shrink: 0;

        .product-img {
          width: 100%;
          height: 100%;
        }

        .product-placeholder {
          width: 100%;
          height: 100%;
          background: $background-secondary;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .product-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 12rpx;

        .product-name {
          font-size: 32rpx;
          font-weight: 600;
          color: $label-primary;
          line-height: 1.3;
        }

        .product-number {
          font-size: 26rpx;
          color: $label-secondary;
        }

        .product-status {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 8rpx;

          .status-text {
            font-size: 26rpx;
            color: $system-blue;
            font-weight: 500;
          }
        }
      }
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    padding: 40rpx;

    .load-more-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16rpx;
      padding: 32rpx 64rpx;
      background: rgba(0, 122, 255, 0.08);
      border: 2rpx solid rgba(0, 122, 255, 0.2);
      border-radius: 50rpx;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;
      box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.1);

      &:active:not(.loading) {
        background: rgba(0, 122, 255, 0.15);
        transform: scale(0.95);
        box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.2);
      }

      &.loading {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .load-more-text {
        font-size: 30rpx;
        color: $system-blue;
        font-weight: 600;
        letter-spacing: -0.48rpx;
      }
    }
  }
}

// 已选物资栏
.selected-items-bar {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 40rpx);
  left: 40rpx;
  right: 40rpx;
  z-index: 99;
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: $background-primary;
  border-radius: 50rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(0, 122, 255, 0.1);

  .selected-items-left {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 16rpx 24rpx;
    border-radius: 36rpx;
    background: rgba(0, 122, 255, 0.05);
    transition: all 0.2s ease;
    cursor: pointer;

    &:active {
      background: rgba(0, 122, 255, 0.1);
      transform: scale(0.98);
    }

    .selected-items-text {
      font-size: 28rpx;
      font-weight: 600;
      color: $label-primary;
    }

    .selected-items-badge {
      background: $system-blue;
      color: white;
      border-radius: 20rpx;
      padding: 4rpx 16rpx;
      font-size: 24rpx;
      font-weight: 600;
      min-width: 40rpx;
      text-align: center;
    }
  }

  :deep(.next-step-button) {
    flex-shrink: 0;
    border-radius: 40rpx;
    padding: 24rpx 48rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.2);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      box-shadow: none;
    }
  }
}

// 购物车列表
.cart-list {
  .empty-cart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;

    .empty-text {
      font-size: 32rpx;
      color: $label-secondary;
      margin: 32rpx 0;
    }
  }

  .cart-item {
    background: $background-primary;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    gap: 24rpx;

    .cart-item-image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 16rpx;
      overflow: hidden;
      flex-shrink: 0;

      .item-img {
        width: 100%;
        height: 100%;
      }

      .item-placeholder {
        width: 100%;
        height: 100%;
        background: $background-secondary;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .cart-item-info {
      flex: 1;

      .item-name {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: $label-primary;
        margin-bottom: 8rpx;
      }

      .item-sku {
        font-size: 28rpx;
        color: $label-secondary;
        margin-bottom: 4rpx;
      }

      .item-number {
        font-size: 24rpx;
        color: $label-secondary;
        margin-bottom: 12rpx;
      }

      .item-price {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .price-label {
          font-size: 24rpx;
          color: $label-secondary;
        }

        .price-value {
          font-size: 28rpx;
          font-weight: 600;
          color: $system-blue;
        }
      }
    }

    .cart-item-actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 16rpx;

      .quantity-controls {
        display: flex;
        align-items: center;
        gap: 16rpx;

        :deep(.quantity-btn) {
          width: 56rpx;
          height: 56rpx;
          border-radius: 28rpx;
          background: $background-secondary;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 32rpx;
          font-weight: 600;
          color: $system-blue;
        }

        .quantity-display {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          padding: 12rpx 24rpx;
          background: rgba(0, 122, 255, 0.05);
          border-radius: 32rpx;
          border: 2rpx solid rgba(0, 122, 255, 0.1);
          min-width: 100rpx;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(0, 122, 255, 0.1);
          }

          &:active {
            transform: scale(0.95);
            background: rgba(0, 122, 255, 0.15);
          }

          .quantity-text {
            font-size: 32rpx;
            font-weight: 600;
            color: $label-primary;
            text-align: center;
          }
        }
      }

      .item-total {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .total-label {
          font-size: 24rpx;
          color: $label-secondary;
        }

        .total-value {
          font-size: 32rpx;
          font-weight: 600;
          color: $system-blue;
        }
      }

      :deep(.remove-btn) {
        width: 56rpx;
        height: 56rpx;
        border-radius: 28rpx;
        background: rgba(255, 59, 48, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 购物车汇总
.cart-summary {
  background: $background-primary;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;

  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .summary-label {
      font-size: 28rpx;
      color: $label-secondary;
    }

    .summary-value {
      font-size: 32rpx;
      font-weight: 600;
      color: $label-primary;

      &.total-amount {
        font-size: 36rpx;
        color: $system-blue;
      }
    }
  }
}

// 已选概览
.selected-summary {
  background: $background-primary;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;

  .summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .summary-title {
      font-size: 32rpx;
      font-weight: 600;
      color: $label-primary;
    }

    .summary-count {
      font-size: 28rpx;
      color: $system-blue;
    }
  }

  .summary-items {
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16rpx 0;
      border-bottom: 2rpx solid $background-secondary;

      &:last-child {
        border-bottom: none;
      }

      .summary-item-name {
        font-size: 28rpx;
        font-weight: 600;
        color: $label-primary;
      }

      .summary-item-sku {
        font-size: 24rpx;
        color: $label-secondary;
      }

      .summary-item-quantity {
        font-size: 28rpx;
        color: $system-blue;
        font-weight: 600;
      }
    }
  }
}

// 订单表单
.order-form {
  background: $background-primary;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;

  .form-section {
    .form-item {
      margin-bottom: 40rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .form-label {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: $label-primary;
        margin-bottom: 16rpx;

        &.required::after {
          content: ' *';
          color: $system-red;
        }
      }
    }
  }
}

// 操作按钮
.step-actions {
  display: flex;
  gap: 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 100rpx);
  position: fixed;
  bottom: 0;
  left: 40rpx;
  right: 40rpx;
  background: $background-primary;
  padding: 40rpx;
  border-top: 2rpx solid $separator;
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  z-index: 100;

  :deep(.step-button) {
    flex: 1;
    border-radius: 50rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }
  }
}

// 产品详情弹窗
:deep(.product-detail-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .product-detail {
    background: $background-primary;
    max-height: 80vh;
    overflow: hidden;
    padding-bottom: calc(env(safe-area-inset-bottom) + 120px);

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 40rpx;
      border-bottom: 2rpx solid $separator;

      .detail-title {
        font-size: 36rpx;
        font-weight: 600;
        color: $label-primary;
      }
    }

    .detail-content {
      padding: 40rpx;
      max-height: 60vh;
      overflow-y: auto;

      .detail-info {
        margin-bottom: 40rpx;

        .detail-number {
          font-size: 28rpx;
          color: $label-secondary;
          margin-bottom: 24rpx;
        }

        .detail-image {
          width: 100%;
          height: 200px;
          border-radius: 24rpx;
          overflow: hidden;

          .detail-img {
            width: 100%;
            height: 100%;
          }

          .detail-img-placeholder {
            width: 100%;
            height: 100%;
            background: $background-secondary;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 16rpx;

            .placeholder-text {
              font-size: 28rpx;
              color: $label-secondary;
              font-weight: 500;
            }
          }
        }
      }

      .detail-skus {
        .skus-title {
          display: block;
          font-size: 32rpx;
          font-weight: 600;
          color: $label-primary;
          margin-bottom: 32rpx;
        }

        .skus-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 24rpx;

          .sku-card {
            background: $background-secondary;
            border-radius: 24rpx;
            padding: 32rpx;
            display: flex;
            align-items: center;
            gap: 24rpx;
            transition: transform 0.2s ease;

            &:active:not(.disabled) {
              transform: scale(0.98);
            }

            &.disabled {
              opacity: 0.5;
              pointer-events: none;
            }

            .sku-image {
              width: 100rpx;
              height: 100rpx;
              border-radius: 16rpx;
              overflow: hidden;
              flex-shrink: 0;

              .sku-img {
                width: 100%;
                height: 100%;
              }

              .sku-img-placeholder {
                width: 100%;
                height: 100%;
                background: $background-primary;
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }

            .sku-info {
              flex: 1;

              .sku-name {
                display: block;
                font-size: 32rpx;
                font-weight: 600;
                color: $label-primary;
                margin-bottom: 8rpx;
              }

              .sku-price {
                font-size: 28rpx;
                color: $system-blue;
                font-weight: 600;
                margin-bottom: 4rpx;
              }

              .sku-stock {
                font-size: 24rpx;
                color: $system-green;
              }
            }

            .sku-action {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 80rpx;
              height: 80rpx;
              background: rgba(0, 122, 255, 0.1);
              border-radius: 40rpx;
            }
          }
        }
      }
    }
  }
}

// 已选物资弹窗
:deep(.selected-items-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .selected-items-detail {
    background: $background-primary;
    max-height: 70vh;
    overflow: hidden;
    padding-bottom: calc(env(safe-area-inset-bottom) + 120px);

    .detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 40rpx;
      border-bottom: 2rpx solid $separator;

      .detail-title {
        font-size: 36rpx;
        font-weight: 600;
        color: $label-primary;
      }
    }

    .selected-items-content {
      padding: 40rpx;
      max-height: 50vh;
      min-height: 30vh;
      overflow-y: auto;

      .empty-selected {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 80rpx 40rpx;

        .empty-text {
          font-size: 32rpx;
          color: $label-secondary;
          margin: 32rpx 0 16rpx;
        }

        .empty-subtitle {
          font-size: 28rpx;
          color: $label-secondary;
        }
      }

      .selected-items-list {
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .selected-item {
          display: flex;
          align-items: center;
          gap: 24rpx;
          padding: 24rpx;
          background: $background-secondary;
          border-radius: 24rpx;

          .selected-item-image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 16rpx;
            overflow: hidden;
            flex-shrink: 0;

            .item-img {
              width: 100%;
              height: 100%;
            }

            .item-placeholder {
              width: 100%;
              height: 100%;
              background: $background-primary;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .selected-item-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .item-name {
              font-size: 30rpx;
              font-weight: 600;
              color: $label-primary;
            }

            .item-sku {
              font-size: 26rpx;
              color: $label-secondary;
            }

            .item-quantity {
              font-size: 24rpx;
              color: $system-blue;
              font-weight: 500;
            }
          }

          .selected-item-price {
            .price-text {
              font-size: 32rpx;
              font-weight: 600;
              color: $system-blue;
            }
          }
        }
      }
    }
  }
}

// 删除确认弹窗
:deep(.delete-confirm-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .delete-confirm-content {
    background: $background-primary;
    border-radius: 32rpx;
    padding: 64rpx 48rpx;
    min-width: 280px;
    max-width: 320px;
    box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);

    .delete-confirm-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 48rpx;

      .delete-confirm-title {
        font-size: 36rpx;
        font-weight: 600;
        color: $label-primary;
        margin: 32rpx 0 16rpx;
        text-align: center;
      }

      .delete-item-info {
        background: rgba(255, 59, 48, 0.05);
        border-radius: 16rpx;
        padding: 24rpx 32rpx;
        margin: 24rpx 0;
        text-align: center;
        border: 2rpx solid rgba(255, 59, 48, 0.1);

        .delete-item-name {
          display: block;
          font-size: 30rpx;
          font-weight: 600;
          color: $label-primary;
          margin-bottom: 8rpx;
        }

        .delete-item-sku {
          font-size: 26rpx;
          color: $label-secondary;
        }
      }

      .delete-confirm-text {
        font-size: 28rpx;
        color: $label-secondary;
        text-align: center;
        line-height: 1.4;
      }
    }

    .delete-confirm-actions {
      display: flex;
      gap: 24rpx;

      :deep(.delete-confirm-button) {
        flex: 1;
        border-radius: 24rpx;
        padding: 28rpx 40rpx;
        font-size: 32rpx;
        font-weight: 600;
        transition: all 0.2s ease;

        .cancel-button {
          background: $background-secondary;
          color: $label-primary;
          border: 2rpx solid $separator;

          &:active {
            transform: scale(0.95);
            background: rgba(142, 142, 147, 0.2);
          }
        }

        .confirm-button {
          background: $system-red;
          color: white;
          border: 2rpx solid $system-red;

          &:active {
            transform: scale(0.95);
            background: rgba(255, 59, 48, 0.8);
          }
        }
      }
    }
  }
}

// 数量编辑弹窗
:deep(.quantity-edit-popup) {
  border-radius: 32rpx 32rpx 0 0;
  .quantity-edit-content {
    background: $background-primary;
    border-radius: 32rpx;
    padding: 64rpx 48rpx;
    min-width: 280px;
    max-width: 320px;
    box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);

    .quantity-edit-header {
      text-align: center;
      margin-bottom: 48rpx;

      .quantity-edit-title {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: $label-primary;
        margin-bottom: 16rpx;
      }

      .quantity-edit-subtitle {
        font-size: 28rpx;
        color: $label-secondary;
        line-height: 1.4;
      }
    }

    .quantity-edit-input-section {
      margin-bottom: 48rpx;

      .quantity-input {
        width: 100%;
        text-align: center;
        font-size: 36rpx;
        font-weight: 600;
        color: $label-primary;
        margin-bottom: 16rpx;
        transition: all 0.2s ease;

        &.invalid-quantity {
          border-color: $system-red;
          box-shadow: 0 0 0 4rpx rgba(255, 59, 48, 0.1);
        }
      }

      .stock-hint {
        text-align: center;

        .stock-text {
          font-size: 24rpx;
          color: $system-green;
          font-weight: 500;
        }
      }

      .error-hint {
        text-align: center;
        margin-top: 16rpx;

        .error-text {
          font-size: 24rpx;
          color: $system-red;
          font-weight: 500;
        }
      }
    }

    .quantity-edit-actions {
      display: flex;
      gap: 24rpx;

      .quantity-edit-button {
        flex: 1;
        border-radius: 24rpx;
        padding: 28rpx 40rpx;
        font-size: 32rpx;
        font-weight: 600;
        transition: all 0.2s ease;

        .cancel-button {
          background: $background-secondary;
          color: $label-primary;
          border: 2rpx solid $separator;

          &:active {
            transform: scale(0.95);
            background: rgba(142, 142, 147, 0.2);
          }
        }

        &.confirm-button {
          background: $system-blue;
          color: white;
          border: 2rpx solid $system-blue;

          &:active {
            transform: scale(0.95);
            background: rgba(0, 122, 255, 0.8);
          }

          &:disabled {
            background: $system-gray-light;
            color: rgba(255, 255, 255, 0.6);
            border-color: $system-gray-light;
            cursor: not-allowed;
            transform: none;
          }
        }
      }
    }
  }
}
</style>
