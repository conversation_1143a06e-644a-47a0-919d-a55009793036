<script setup lang="ts">
import { getMyDetailApi, updateMyDetailApi } from '@/api/modules/rescue/user/user'
import type { IRescueUser } from '@/api/interface/rescue/user/user'
import { getGenderOptions, getPoliticsStatusOptions, getBloodTypeOptions } from '@/enums/rescue'

// 页面状态
const pageLoaded = ref(false)
const loading = ref(false)
const editing = ref(false)
const saving = ref(false)

// 用户信息
const userInfo = ref<IRescueUser.UserInfoVO | null>(null)
const editForm = ref<IRescueUser.UpdateMyDetailDTO>({})

// 使用枚举作为数据源
const genderOptions = getGenderOptions().map(item => ({
  label: item.name,
  value: item.code
}))

const politicsOptions = getPoliticsStatusOptions().map(item => ({
  label: item.name,
  value: item.code
}))

const bloodTypeOptions = getBloodTypeOptions().map(item => ({
  label: item.name,
  value: item.code
}))

// 获取用户详情
async function fetchUserDetail() {
  try {
    loading.value = true
    const response = await getMyDetailApi()
    if (response.data) {
      userInfo.value = response.data
      // 初始化编辑表单
      editForm.value = {
        name: response.data.name,
        nickName: response.data.nickName,
        phone: response.data.phone,
        avatar: response.data.avatar,
        sex: response.data.sex,
        birthday: response.data.birthday,
        identityCard: response.data.identityCard,
        identityStartDate: response.data.identityStartDate,
        identityEndDate: response.data.identityEndDate,
        passportNumber: response.data.passportNumber,
        politicsStatus: response.data.politicsStatus,
        bloodType: response.data.bloodType,
        remark: response.data.remark,
        emergencyContact: response.data.emergencyContact,
        emergencyContactPhone: response.data.emergencyContactPhone,
        medicalHistory: response.data.medicalHistory,
        allergiesHistory: response.data.allergiesHistory,
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 开始编辑
function startEdit() {
  editing.value = true
}

// 取消编辑
function cancelEdit() {
  editing.value = false
  // 重置表单数据
  if (userInfo.value) {
    editForm.value = {
      name: userInfo.value.name,
      nickName: userInfo.value.nickName,
      phone: userInfo.value.phone,
      avatar: userInfo.value.avatar,
      sex: userInfo.value.sex,
      birthday: userInfo.value.birthday,
      identityCard: userInfo.value.identityCard,
      identityStartDate: userInfo.value.identityStartDate,
      identityEndDate: userInfo.value.identityEndDate,
      passportNumber: userInfo.value.passportNumber,
      politicsStatus: userInfo.value.politicsStatus,
      bloodType: userInfo.value.bloodType,
      remark: userInfo.value.remark,
      emergencyContact: userInfo.value.emergencyContact,
      emergencyContactPhone: userInfo.value.emergencyContactPhone,
      medicalHistory: userInfo.value.medicalHistory,
      allergiesHistory: userInfo.value.allergiesHistory,
    }
  }
}

// 保存用户信息
async function saveUserInfo() {
  try {
    saving.value = true
    const response = await updateMyDetailApi(editForm.value)
    if (response.data) {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
      editing.value = false
      // 重新获取用户信息
      await fetchUserDetail()
    }
  } catch (error) {
    console.error('保存用户信息失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'none',
    })
  } finally {
    saving.value = false
  }
}



// 选择头像
function selectAvatar() {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      // 这里应该上传图片到服务器，然后获取URL
      // 暂时使用本地路径
      editForm.value.avatar = res.tempFilePaths[0]
      uni.showToast({
        title: '头像上传功能开发中',
        icon: 'none',
      })
    },
  })
}

// 页面加载动画
function startPageAnimation() {
  pageLoaded.value = true
}

onMounted(() => {
  startPageAnimation()
  fetchUserDetail()
})
</script>

<template>
  <view class="detail-page" :class="{ loaded: pageLoaded }">
    <!-- 编辑操作栏 -->
    <view class="edit-toolbar">
      <view v-if="!editing" class="edit-btn" @tap="startEdit">
        <wd-icon name="edit" size="36rpx" color="#007AFF" />
        <view class="edit-text">编辑</view>
      </view>
      <view v-else class="edit-actions">
        <view class="cancel-btn" @tap="cancelEdit">取消</view>
        <view class="save-btn" @tap="saveUserInfo">
          <wd-loading v-if="saving" size="24rpx" color="#ffffff" />
          <text v-else>保存</text>
        </view>
      </view>
    </view>

    <!-- 主内容 -->
    <scroll-view class="main-content" scroll-y enhanced :show-scrollbar="false">
      <!-- 头像区域 -->
      <view class="avatar-section">
        <view class="avatar-wrapper" @tap="editing ? selectAvatar() : null">
          <image
            :src="editForm.avatar || userInfo?.avatar || '/static/images/default-avatar.png'"
            class="user-avatar"
            mode="aspectFill"
          />
          <view v-if="editing" class="avatar-edit-overlay">
            <wd-icon name="camera" size="48rpx" color="#ffffff" />
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">基本信息</view>
        <view class="info-list">
          <view class="info-item">
            <view class="item-label">用户名</view>
            <view class="item-value readonly">{{ userInfo?.username || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">编号</view>
            <view class="item-value readonly">
              {{ userInfo?.numberPrefix ? `${userInfo.numberPrefix}${userInfo.number}` : '-' }}
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">昵称</view>
            <input
              v-if="editing"
              v-model="editForm.nickName"
              class="item-input"
              placeholder="请输入昵称"
            />
            <view v-else class="item-value">{{ userInfo?.nickName || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">真实姓名</view>
            <input
              v-if="editing"
              v-model="editForm.name"
              class="item-input"
              placeholder="请输入真实姓名"
            />
            <view v-else class="item-value">{{ userInfo?.name || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">职位</view>
            <view class="item-value readonly">{{ userInfo?.post || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">手机号</view>
            <input
              v-if="editing"
              v-model="editForm.phone"
              class="item-input"
              placeholder="请输入手机号"
              type="number"
            />
            <view v-else class="item-value">{{ userInfo?.phone || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">性别</view>
            <picker
              v-if="editing"
              :range="genderOptions"
              range-key="label"
              @change="(e) => editForm.sex = genderOptions[e.detail.value].value"
            >
              <view class="item-picker">
                {{ genderOptions.find(item => item.value === editForm.sex)?.label || '请选择' }}
                <wd-icon name="arrow-right" size="32rpx" color="#C7C7CC" />
              </view>
            </picker>
            <view v-else class="item-value">
              {{ genderOptions.find(item => item.value === userInfo?.sex)?.label || '-' }}
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">生日</view>
            <picker
              v-if="editing"
              mode="date"
              @change="(e) => editForm.birthday = e.detail.value"
            >
              <view class="item-picker">
                {{ editForm.birthday || '请选择生日' }}
                <wd-icon name="arrow-right" size="32rpx" color="#C7C7CC" />
              </view>
            </picker>
            <view v-else class="item-value">{{ userInfo?.birthday || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">身份证号</view>
            <input
              v-if="editing"
              v-model="editForm.identityCard"
              class="item-input"
              placeholder="请输入身份证号"
            />
            <view v-else class="item-value">{{ userInfo?.identityCard || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">政治面貌</view>
            <picker
              v-if="editing"
              :range="politicsOptions"
              range-key="label"
              @change="(e) => editForm.politicsStatus = politicsOptions[e.detail.value].value"
            >
              <view class="item-picker">
                {{ politicsOptions.find(item => item.value === editForm.politicsStatus)?.label || '请选择' }}
                <wd-icon name="arrow-right" size="32rpx" color="#C7C7CC" />
              </view>
            </picker>
            <view v-else class="item-value">
              {{ politicsOptions.find(item => item.value === userInfo?.politicsStatus)?.label || '-' }}
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">血型</view>
            <picker
              v-if="editing"
              :range="bloodTypeOptions"
              range-key="label"
              @change="(e) => editForm.bloodType = bloodTypeOptions[e.detail.value].value"
            >
              <view class="item-picker">
                {{ bloodTypeOptions.find(item => item.value === editForm.bloodType)?.label || '请选择' }}
                <wd-icon name="arrow-right" size="32rpx" color="#C7C7CC" />
              </view>
            </picker>
            <view v-else class="item-value">
              {{ bloodTypeOptions.find(item => item.value === userInfo?.bloodType)?.label || '-' }}
            </view>
          </view>
        </view>
      </view>

      <!-- 紧急联系人 -->
      <view class="info-section">
        <view class="section-title">紧急联系人</view>
        <view class="info-list">
          <view class="info-item">
            <view class="item-label">联系人姓名</view>
            <input
              v-if="editing"
              v-model="editForm.emergencyContact"
              class="item-input"
              placeholder="请输入紧急联系人姓名"
            />
            <view v-else class="item-value">{{ userInfo?.emergencyContact || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">联系人电话</view>
            <input
              v-if="editing"
              v-model="editForm.emergencyContactPhone"
              class="item-input"
              placeholder="请输入紧急联系人电话"
              type="number"
            />
            <view v-else class="item-value">{{ userInfo?.emergencyContactPhone || '-' }}</view>
          </view>
        </view>
      </view>

      <!-- 健康信息 -->
      <view class="info-section">
        <view class="section-title">健康信息</view>
        <view class="info-list">
          <view class="info-item">
            <view class="item-label">病史</view>
            <textarea
              v-if="editing"
              v-model="editForm.medicalHistory"
              class="item-textarea"
              placeholder="请输入病史信息"
              maxlength="500"
            />
            <view v-else class="item-value multiline">{{ userInfo?.medicalHistory || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">过敏史</view>
            <textarea
              v-if="editing"
              v-model="editForm.allergiesHistory"
              class="item-textarea"
              placeholder="请输入过敏史信息"
              maxlength="500"
            />
            <view v-else class="item-value multiline">{{ userInfo?.allergiesHistory || '-' }}</view>
          </view>
        </view>
      </view>

      <!-- 其他信息 -->
      <view class="info-section">
        <view class="section-title">其他信息</view>
        <view class="info-list">
          <view class="info-item">
            <view class="item-label">护照号码</view>
            <input
              v-if="editing"
              v-model="editForm.passportNumber"
              class="item-input"
              placeholder="请输入护照号码"
            />
            <view v-else class="item-value">{{ userInfo?.passportNumber || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">备注</view>
            <textarea
              v-if="editing"
              v-model="editForm.remark"
              class="item-textarea"
              placeholder="请输入备注信息"
              maxlength="500"
            />
            <view v-else class="item-value multiline">{{ userInfo?.remark || '-' }}</view>
          </view>
        </view>
      </view>

      <!-- 系统信息 -->
      <view class="info-section">
        <view class="section-title">系统信息</view>
        <view class="info-list">
          <view class="info-item">
            <view class="item-label">部门</view>
            <view class="item-value readonly">{{ userInfo?.deptName || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">角色</view>
            <view class="item-value readonly">
              <view v-if="userInfo?.roles?.length" class="roles-list">
                <view
                  v-for="role in userInfo.roles"
                  :key="role.id"
                  class="role-tag"
                >
                  {{ role.roleName }}
                </view>
              </view>
              <text v-else>-</text>
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">总出勤时长</view>
            <view class="item-value readonly">
              {{ userInfo?.totalDutyDuration ? `${userInfo.totalDutyDuration}小时` : '-' }}
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">年度出勤时长</view>
            <view class="item-value readonly">
              {{ userInfo?.yearDutyDuration ? `${userInfo.yearDutyDuration}小时` : '-' }}
            </view>
          </view>

          <view class="info-item">
            <view class="item-label">审批时间</view>
            <view class="item-value readonly">{{ userInfo?.approveTime || '-' }}</view>
          </view>

          <view class="info-item">
            <view class="item-label">状态</view>
            <view class="item-value readonly">
              <view class="status-tag" :class="{ active: userInfo?.enable }">
                {{ userInfo?.enable ? '启用' : '禁用' }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom" />
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
// Apple设计系统颜色
$system-blue: #007aff;
$system-green: #34c759;
$system-orange: #ff9500;
$system-red: #ff3b30;
$system-gray: #8e8e93;
$system-gray2: #aeaeb2;
$system-gray3: #c7c7cc;
$system-gray4: #d1d1d6;
$system-gray5: #e5e5ea;
$system-gray6: #f2f2f7;

// 文本颜色
$label-primary: #000000;
$label-secondary: rgba(60, 60, 67, 0.6);
$label-tertiary: rgba(60, 60, 67, 0.3);

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f2f2f7;

// 页面容器
.detail-page {
  min-height: 100vh;
  background: $background-secondary;
  position: relative;
}

// 编辑工具栏
.edit-toolbar {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1000;
  padding: 20rpx 40rpx;
  padding-top: calc(20rpx + var(--status-bar-height, 0));

  .edit-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    border: 1rpx solid rgba(0, 122, 255, 0.2);

    .edit-text {
      font-size: 28rpx;
      color: $system-blue;
      font-weight: 500;
    }

    &:active {
      background: rgba(0, 122, 255, 0.1);
    }
  }

  .edit-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    padding: 8rpx;
    border-radius: 24rpx;
    border: 1rpx solid rgba(60, 60, 67, 0.12);

    .cancel-btn {
      font-size: 28rpx;
      color: $system-gray;
      font-weight: 500;
      cursor: pointer;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;

      &:active {
        background: rgba(142, 142, 147, 0.1);
      }
    }

    .save-btn {
      background: $system-blue;
      color: #ffffff;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8rpx;
      min-width: 60rpx;
      justify-content: center;

      &:active {
        background: rgba(0, 122, 255, 0.8);
      }
    }
  }
}

// 主内容
.main-content {
  padding-top: 40rpx;
  height: 100vh;
}

// 头像区域
.avatar-section {
  display: flex;
  justify-content: center;
  padding: 40rpx;
  margin-bottom: 32rpx;

  .avatar-wrapper {
    position: relative;
    width: 160rpx;
    height: 160rpx;

    .user-avatar {
      width: 100%;
      height: 100%;
      border-radius: 80rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    }

    .avatar-edit-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}

// 信息区域
.info-section {
  margin: 0 40rpx 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 700;
    color: $label-primary;
    margin-bottom: 24rpx;
    letter-spacing: -0.5rpx;
  }

  .info-list {
    background: $background-primary;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  }
}

// 信息项
.info-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  border-bottom: 1rpx solid $system-gray6;

  &:last-child {
    border-bottom: none;
  }

  .item-label {
    width: 200rpx;
    font-size: 28rpx;
    color: $label-primary;
    font-weight: 500;
    flex-shrink: 0;
    line-height: 1.4;
  }

  .item-value {
    flex: 1;
    font-size: 28rpx;
    color: $label-secondary;
    line-height: 1.4;
    word-break: break-all;

    &.readonly {
      color: $label-tertiary;
    }

    &.multiline {
      white-space: pre-wrap;
      line-height: 1.6;
    }
  }

  .item-input {
    flex: 1;
    font-size: 28rpx;
    color: $label-primary;
    background: transparent;
    border: none;
    outline: none;
    line-height: 1.4;

    &::placeholder {
      color: $system-gray3;
    }
  }

  .item-textarea {
    flex: 1;
    font-size: 28rpx;
    color: $label-primary;
    background: transparent;
    border: none;
    outline: none;
    min-height: 120rpx;
    line-height: 1.6;
    resize: none;

    &::placeholder {
      color: $system-gray3;
    }
  }

  .item-picker {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    color: $label-primary;
    cursor: pointer;
  }

  .roles-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;

    .role-tag {
      padding: 8rpx 16rpx;
      background: rgba(0, 122, 255, 0.1);
      border: 1rpx solid rgba(0, 122, 255, 0.2);
      border-radius: 16rpx;
      font-size: 20rpx;
      color: $system-blue;
      font-weight: 600;
    }
  }

  .status-tag {
    padding: 8rpx 16rpx;
    background: rgba(255, 59, 48, 0.1);
    border: 1rpx solid rgba(255, 59, 48, 0.2);
    border-radius: 16rpx;
    font-size: 20rpx;
    color: $system-red;
    font-weight: 600;

    &.active {
      background: rgba(52, 199, 89, 0.1);
      border: 1rpx solid rgba(52, 199, 89, 0.2);
      color: $system-green;
    }
  }
}

// 安全区域
.safe-area-bottom {
  height: 120rpx;
}
</style>
