<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '审批状态选择器',
  },
}
</route>

<script setup lang="ts">
import type { IOrder } from '@/api/interface/rescue/order/order'
import OrderCard from '@/components/rescue/order/OrderCard.vue'
import OrderReviewDialog from '@/components/rescue/order/OrderReviewDialog.vue'
import ReviewStatusSelect from '@/components/rescue/ReviewStatusSelect.vue'

// 状态选择
const selectedStatus = ref('')
const selectedStatus2 = ref('')
const selectedStatus3 = ref('')
const selectedStatusSmall = ref('')
const selectedStatusLarge = ref('')
const errorMessage = ref('请选择审批状态')

// 弹窗状态
const dialogVisible = ref(false)

// 模拟订单数据
const mockOrder: IOrder.OrderVO = {
  id: 1,
  tenantId: 'tenant001',
  deptId: 1,
  workId: 1,
  workNumber: 'WK20241201001',
  orderNumber: 'ORD20241201001',
  type: 'OUTBOUND',
  reason: '紧急救援物资申请',
  applyUserId: 1,
  applyName: '张三',
  applyPhone: '13800138000',
  operate: true,
  totalAmount: 5,
  totalPrice: 1250.0,
  status: 'OutboundComplete', // 改为 OutboundComplete 来测试申请入库按钮
  remark: '需要立即处理',
  createTime: '2024-12-01T10:30:00Z',
  updateTime: '2024-12-01T10:30:00Z',
  reviewName: '',
  reviewPhone: '',
  reviewOpinion: '',
}

// 模拟待审批订单数据
const mockPendingOrder: IOrder.OrderVO = {
  id: 2,
  tenantId: 'tenant001',
  deptId: 1,
  workId: 2,
  workNumber: 'WK20241201002',
  orderNumber: 'ORD20241201002',
  type: 'OUTBOUND',
  reason: '日常物资申请',
  applyUserId: 1,
  applyName: '李四',
  applyPhone: '13800138001',
  operate: true,
  totalAmount: 3,
  totalPrice: 800.0,
  status: 'OutboundApply', // 待审批状态
  remark: '常规申请',
  createTime: '2024-12-01T11:30:00Z',
  updateTime: '2024-12-01T11:30:00Z',
  reviewName: '',
  reviewPhone: '',
  reviewOpinion: '',
}

// 事件处理
function handleStatusChange(value: string) {
  console.log('状态变更:', value)
  selectedStatus.value = value
}

function handleStatusChange2(value: string) {
  console.log('状态变更2:', value)
  selectedStatus2.value = value
}

function handleStatusChange3(value: string) {
  console.log('状态变更3:', value)
  selectedStatus3.value = value
  if (value) {
    errorMessage.value = ''
  }
}

function handleStatusChangeSmall(value: string) {
  console.log('小尺寸状态变更:', value)
  selectedStatusSmall.value = value
}

function handleStatusChangeLarge(value: string) {
  console.log('大尺寸状态变更:', value)
  selectedStatusLarge.value = value
}

function handleReview(order: IOrder.OrderVO) {
  console.log('审批订单:', order)
  dialogVisible.value = true
}

function handleApplyInbound(order: IOrder.OrderVO) {
  console.log('申请入库:', order)
  uni.showToast({
    title: '申请入库功能开发中',
    icon: 'none',
    duration: 2000,
  })
}

function handleReviewSuccess() {
  console.log('审批成功')
  uni.showToast({
    title: '审批操作成功',
    icon: 'success',
  })
}
</script>

<template>
  <view class="demo-container">
    <view class="demo-section">
      <view class="section-title">ReviewStatusSelect 组件演示</view>

      <!-- 基础用法 -->
      <view class="demo-item">
        <view class="demo-label">基础用法</view>
        <ReviewStatusSelect v-model="selectedStatus" @update:model-value="handleStatusChange" />
      </view>

      <!-- 带标题 -->
      <view class="demo-item">
        <view class="demo-label">带标题</view>
        <ReviewStatusSelect
          v-model="selectedStatus2"
          show-title
          title="审批结果"
          required
          @update:model-value="handleStatusChange2"
        />
      </view>

      <!-- 带错误信息 -->
      <view class="demo-item">
        <view class="demo-label">带错误信息</view>
        <ReviewStatusSelect
          v-model="selectedStatus3"
          :error-message="errorMessage"
          @update:model-value="handleStatusChange3"
        />
      </view>

      <!-- 不同尺寸 -->
      <view class="demo-item">
        <view class="demo-label">不同尺寸</view>
        <view class="size-demos">
          <ReviewStatusSelect
            v-model="selectedStatusSmall"
            size="small"
            @update:model-value="handleStatusChangeSmall"
          />
          <ReviewStatusSelect
            v-model="selectedStatusLarge"
            size="large"
            @update:model-value="handleStatusChangeLarge"
          />
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">OrderCard 组件演示</view>

      <!-- 出库完成订单（显示申请入库按钮） -->
      <view class="demo-item">
        <view class="demo-label">出库完成订单（申请入库按钮）</view>
        <OrderCard
          :order="mockOrder"
          :show-review-button="true"
          @review="handleReview"
          @apply-inbound="handleApplyInbound"
        />
      </view>

      <!-- 待审批订单（显示立即审批按钮） -->
      <view class="demo-item">
        <view class="demo-label">待审批订单（立即审批按钮）</view>
        <OrderCard
          :order="mockPendingOrder"
          :show-review-button="true"
          @review="handleReview"
          @apply-inbound="handleApplyInbound"
        />
      </view>
    </view>

    <!-- 审批弹窗 -->
    <OrderReviewDialog
      :id="mockOrder.id"
      v-model="dialogVisible"
      :order-info="{
        orderNumber: mockOrder.orderNumber,
        reason: mockOrder.reason,
        applyName: mockOrder.applyName,
      }"
      @success="handleReviewSuccess"
    />
  </view>
</template>

<style lang="scss" scoped>
.demo-container {
  padding: 32rpx;
  background: #f2f2f7;
  min-height: 100vh;
}

.demo-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 24rpx;
  display: block;
}

.demo-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.demo-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #86868b;
  margin-bottom: 16rpx;
  display: block;
}

.size-demos {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
</style>
