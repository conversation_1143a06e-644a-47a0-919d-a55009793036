<script setup lang="ts">
import type { ISysDept } from '@/api/interface/system/admin/dept'
import DeptSelect from '@/components/common/DeptSelect.vue'

// 响应式数据
const singleDeptId = ref<number | null>(null)
const singleDeptData = ref<ISysDept.Tree | null>(null)

const multipleDeptIds = ref<number[]>([])
const multipleDeptData = ref<ISysDept.Tree[]>([])

const disabledDeptId = ref<number | null>(1) // 设置一个默认值
const nonClearableDeptId = ref<number | null>(null)

const cascadeDeptIds = ref<number[]>([])
const cascadeDeptData = ref<ISysDept.Tree[]>([])

// 事件处理
function handleSingleChange(value: number | null, data?: ISysDept.Tree) {
  console.log('单选变化:', value, data)
  singleDeptData.value = data || null

  // 显示选择结果
  if (data) {
    uni.showToast({
      title: `已选择: ${data.name}`,
      icon: 'none',
    })
  }
}

function handleMultipleChange(value: number[], data?: ISysDept.Tree[]) {
  console.log('多选变化:', value, data)
  multipleDeptData.value = data || []

  // 显示选择结果
  const count = Array.isArray(value) ? value.length : 0
  uni.showToast({
    title: `已选择 ${count} 个部门`,
    icon: 'none',
  })
}

function handleCascadeChange(value: number[], data?: ISysDept.Tree[]) {
  console.log('级联选择变化:', value, data)
  cascadeDeptData.value = data || []
}

// 操作方法
function clearAll() {
  singleDeptId.value = null
  singleDeptData.value = null
  multipleDeptIds.value = []
  multipleDeptData.value = []
  nonClearableDeptId.value = null
  cascadeDeptIds.value = []
  cascadeDeptData.value = []

  uni.showToast({
    title: '已清空所有选择',
    icon: 'success',
  })
}

function setDefaultValues() {
  singleDeptId.value = 1
  multipleDeptIds.value = [1, 2]
  nonClearableDeptId.value = 1
  cascadeDeptIds.value = [1]

  uni.showToast({
    title: '已设置默认值',
    icon: 'success',
  })
}

function refreshData() {
  uni.showLoading({
    title: '刷新中...',
  })

  // 模拟刷新
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '数据已刷新',
      icon: 'success',
    })
  }, 1000)
}

function goBack() {
  uni.navigateBack()
}
</script>

<template>
  <view class="dept-select-demo">
    <uni-nav-bar title="部门选择组件演示" left-icon="back" @click-left="goBack" />

    <scroll-view class="content" scroll-y>
      <!-- 单选模式 -->
      <view class="demo-section">
        <view class="section-title">单选模式</view>
        <view class="form-item">
          <view class="label">选择部门：</view>
          <DeptSelect
            v-model="singleDeptId"
            placeholder="请选择部门"
            @change="handleSingleChange"
          />
        </view>
        <view class="result">
          <view class="result-label">选中值：</view>
          <view class="result-value">
            {{ singleDeptId || '未选择' }}
          </view>
        </view>
        <view v-if="singleDeptData" class="result">
          <view class="result-label">选中部门：</view>
          <view class="result-value">
            {{ singleDeptData.name }}
          </view>
        </view>
      </view>

      <!-- 多选模式 -->
      <view class="demo-section">
        <view class="section-title">多选模式</view>
        <view class="form-item">
          <view class="label">选择多个部门：</view>
          <DeptSelect
            v-model="multipleDeptIds"
            :multiple="true"
            placeholder="请选择部门"
            :max-display-tags="3"
            @change="handleMultipleChange"
          />
        </view>
        <view class="result">
          <view class="result-label">选中值：</view>
          <view class="result-value">
            {{ JSON.stringify(multipleDeptIds) }}
          </view>
        </view>
        <view v-if="multipleDeptData.length > 0" class="result">
          <view class="result-label">选中部门：</view>
          <view class="dept-list">
            <view v-for="dept in multipleDeptData" :key="dept.id" class="dept-item">
              {{ dept.name }}
            </view>
          </view>
        </view>
      </view>

      <!-- 禁用状态 -->
      <view class="demo-section">
        <view class="section-title">禁用状态</view>
        <view class="form-item">
          <view class="label">禁用的选择器：</view>
          <DeptSelect v-model="disabledDeptId" disabled placeholder="已禁用" />
        </view>
      </view>

      <!-- 不可清空 -->
      <view class="demo-section">
        <view class="section-title">不可清空</view>
        <view class="form-item">
          <view class="label">不可清空：</view>
          <DeptSelect v-model="nonClearableDeptId" :clearable="false" placeholder="请选择部门" />
        </view>
      </view>

      <!-- 非严格模式（多选） -->
      <view class="demo-section">
        <view class="section-title">非严格模式（父子关联）</view>
        <view class="form-item">
          <view class="label">父子关联选择：</view>
          <DeptSelect
            v-model="cascadeDeptIds"
            :multiple="true"
            :check-strictly="false"
            placeholder="选择部门（父子关联）"
            @change="handleCascadeChange"
          />
        </view>
        <view class="result">
          <view class="result-label">选中值：</view>
          <view class="result-value">
            {{ JSON.stringify(cascadeDeptIds) }}
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="action-btn" @click="clearAll">清空所有选择</button>
        <button class="action-btn" @click="setDefaultValues">设置默认值</button>
        <button class="action-btn" @click="refreshData">刷新数据</button>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
.dept-select-demo {
  height: 100vh;
  background-color: #f5f5f5;
}

.content {
  flex: 1;
  padding: 32rpx;
  box-sizing: border-box;
}

.demo-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
  border-left: 8rpx solid #007aff;
  padding-left: 16rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.result {
  margin-top: 24rpx;
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 6rpx solid #007aff;
}

.result-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 16rpx;
}

.result-value {
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
}

.dept-list {
  margin-top: 8rpx;
}

.dept-item {
  display: inline-block;
  padding: 4rpx 12rpx;
  margin: 4rpx 8rpx 4rpx 0;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 6rpx;
  font-size: 24rpx;
}

.action-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.action-btn {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  letter-spacing: -0.2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 2rpx 8rpx rgba(0, 122, 255, 0.25),
    0 0 0 1rpx rgba(0, 122, 255, 0.1);

  &:active {
    transform: scale(0.96);
    background: linear-gradient(135deg, #0056cc 0%, #007aff 100%);
    box-shadow:
      0 1rpx 4rpx rgba(0, 122, 255, 0.4),
      0 0 0 1rpx rgba(0, 122, 255, 0.2);
  }

  &:nth-child(2) {
    background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
    box-shadow:
      0 2rpx 8rpx rgba(52, 199, 89, 0.25),
      0 0 0 1rpx rgba(52, 199, 89, 0.1);

    &:active {
      background: linear-gradient(135deg, #248a3d 0%, #34c759 100%);
      box-shadow:
        0 1rpx 4rpx rgba(52, 199, 89, 0.4),
        0 0 0 1rpx rgba(52, 199, 89, 0.2);
    }
  }

  &:nth-child(3) {
    background: linear-gradient(135deg, #ff9500 0%, #ff9f0a 100%);
    box-shadow:
      0 2rpx 8rpx rgba(255, 149, 0, 0.25),
      0 0 0 1rpx rgba(255, 149, 0, 0.1);

    &:active {
      background: linear-gradient(135deg, #c7750a 0%, #ff9500 100%);
      box-shadow:
        0 1rpx 4rpx rgba(255, 149, 0, 0.4),
        0 0 0 1rpx rgba(255, 149, 0, 0.2);
    }
  }
}
</style>
