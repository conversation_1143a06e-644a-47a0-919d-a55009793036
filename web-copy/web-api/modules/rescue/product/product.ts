import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IProduct } from 'src/api/interface/rescue/product/product'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getProductListApi = (params: IProduct.Query) => {
  return http.post<IPage<IProduct.Row>>(RESCUE_MODULE + `/product/list`, params)
}

/**
* 物资选择器
* @param params
* @returns {*}
*/
export const getProductSelectApi = (params: IProduct.Query) => {
  return http.post<IPage<IProduct.ProductSelectVO>>(RESCUE_MODULE + `/product/select`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createProductApi = (params: IProduct.ProductCreateDTO) => {
  return http.post(RESCUE_MODULE + `/product/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateProductApi = (params: IProduct.ProductCreateDTO) => {
  return http.put(RESCUE_MODULE + `/product/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeProductApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/product/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getProductDetailApi = (params: { id: number }) => {
  return http.get<IProduct.Row>(RESCUE_MODULE + `/product/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importProductExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/product/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportProductExcelApi  = (params: IProduct.Query) => {
  return http.download(RESCUE_MODULE + `/product/export`, params)
}
