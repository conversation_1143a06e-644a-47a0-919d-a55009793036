import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IProductSku } from '@/api/interface/rescue/product/productSku'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getProductSkuListApi = (params: IProductSku.Query) => {
  return http.get<IPage<IProductSku.Row>>(RESCUE_MODULE + `/product-sku/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createProductSkuApi = (params: IProductSku.Form) => {
  return http.post(RESCUE_MODULE + `/product-sku/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateProductSkuApi = (params: IProductSku.Form) => {
  return http.put(RESCUE_MODULE + `/product-sku/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeProductSkuApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/product-sku/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getProductSkuDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IProductSku.Row>(RESCUE_MODULE + `/product-sku/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importProductSkuExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/product-sku/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportProductSkuExcelApi  = (params: IProductSku.Query) => {
  return http.download(RESCUE_MODULE + `/product-sku/export`, params)
}
