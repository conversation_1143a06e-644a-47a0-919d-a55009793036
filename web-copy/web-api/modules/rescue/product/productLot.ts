import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IProductLot } from '@/api/interface/rescue/product/productLot'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getProductLotListApi = (params: IProductLot.Query) => {
  return http.get<IPage<IProductLot.Row>>(RESCUE_MODULE + `/product-lot/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createProductLotApi = (params: IProductLot.Form) => {
  return http.post(RESCUE_MODULE + `/product-lot/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateProductLotApi = (params: IProductLot.Form) => {
  return http.put(RESCUE_MODULE + `/product-lot/update`, params)
}

/**
* 检修
* @param params
* @returns {*}
*/
export const overhaulProductLotApi = (params: IProductLot.OverhaulForm) => {
  return http.put(RESCUE_MODULE + `/product-lot/overhaul`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeProductLotApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/product-lot/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getProductLotDetailApi = (params: { id: number }) => {
  return http.get<IProductLot.Row>(RESCUE_MODULE + `/product-lot/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importProductLotExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/product-lot/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportProductLotExcelApi  = (params: IProductLot.Query) => {
  return http.download(RESCUE_MODULE + `/product-lot/export`, params)
}
