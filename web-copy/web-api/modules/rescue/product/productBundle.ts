import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IProductBundle } from '@/api/interface/rescue/product/productBundle'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getProductBundleListApi = (params: IProductBundle.Query) => {
  return http.get<IPage<IProductBundle.Row>>(RESCUE_MODULE + `/product-bundle/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createProductBundleApi = (params: IProductBundle.Form) => {
  return http.post(RESCUE_MODULE + `/product-bundle/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateProductBundleApi = (params: IProductBundle.Form) => {
  return http.put(RESCUE_MODULE + `/product-bundle/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeProductBundleApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/product-bundle/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getProductBundleDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IProductBundle.Row>(RESCUE_MODULE + `/product-bundle/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importProductBundleExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/product-bundle/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportProductBundleExcelApi  = (params: IProductBundle.Query) => {
  return http.download(RESCUE_MODULE + `/product-bundle/export`, params)
}
