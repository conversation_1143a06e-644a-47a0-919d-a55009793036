import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IUserInfo } from '@/api/interface/rescue/user/userInfo'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getUserInfoListApi = (params: IUserInfo.Query) => {
  return http.get<IPage<IUserInfo.Row>>(RESCUE_MODULE + `/user-info/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createUserInfoApi = (params: IUserInfo.Form) => {
  return http.post(RESCUE_MODULE + `/user-info/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateUserInfoApi = (params: IUserInfo.Form) => {
  return http.put(RESCUE_MODULE + `/user-info/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeUserInfoApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/user-info/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getUserInfoDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IUserInfo.Row>(RESCUE_MODULE + `/user-info/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importUserInfoExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/user-info/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportUserInfoExcelApi  = (params: IUserInfo.Query) => {
  return http.download(RESCUE_MODULE + `/user-info/export`, params)
}

/**
* 批量绑定用户和部门
* @param params
* @returns {*}
*/
export const bindUserInfoDeptApi = (params: { userIds: number[], deptIds: number[] }) => {
  return http.post(RESCUE_MODULE + `/user-info/bind`, params)
}
