import http from '@/api';
import { RESCUE_MODULE } from 'src/api/helper/prefix';
import type { IRescueUser } from 'src/api/interface/rescue/user/user';
import type { IPage } from 'src/api/interface';

/**
 * 获取Rescue用户列表
 * @param params 查询参数
 * @returns 用户分页数据
 */
export const getRescueUserList = (params: IRescueUser.Query) => {
  return http.post<IPage<IRescueUser.Info>>(RESCUE_MODULE + `/rescue-user/listPage`, params, { loading: false });
};

/**
 * 根据ID获取Rescue用户详情
 * @param params ID参数
 * @returns 用户详情
 */
export const getRescueUserDetail = (params: { id: string }) => {
  const { id } = params;
  return http.get<IRescueUser.Info>(RESCUE_MODULE + `/rescue-user/${id}`);
};

/**
 * 添加Rescue用户
 * @param params 用户表单数据
 * @returns 添加结果
 */
export const addRescueUser = (params: IRescueUser.Form) => {
  return http.post(RESCUE_MODULE + `/rescue-user`, params);
};

/**
 * 修改Rescue用户
 * @param params 用户表单数据
 * @returns 修改结果
 */
export const editRescueUser = (params: IRescueUser.Form) => {
  return http.put(RESCUE_MODULE + `/rescue-user`, params);
};

/**
 * 删除Rescue用户
 * @param params ID数组
 * @returns 删除结果
 */
export const deleteRescueUser = (params: { ids: number[] }) => {
  return http.delete(RESCUE_MODULE + `/rescue-user`, params);
};

/**
 * 批量操作Rescue用户
 * @param params 批量操作参数
 * @returns 操作结果
 */
export const batchRescueUserOperation = (params: IRescueUser.BatchOperation) => {
  return http.post(RESCUE_MODULE + `/rescue-user/batch`, params);
};

/**
 * 搜索Rescue用户（用于选择器）
 * @param params 搜索关键词
 * @returns 用户选项列表
 */
export const searchRescueUserList = (params: { searchKey: string }) => {
  return http.get<IRescueUser.Options[]>(RESCUE_MODULE + `/rescue-user/search`, params);
};

/**
 * 批量获取Rescue用户信息
 * @param params 用户ID数组
 * @returns 用户信息列表
 */
export const batchGetRescueUsers = (params: { ids: number[] }) => {
  return http.post<IRescueUser.Info[]>(RESCUE_MODULE + `/rescue-user/batchGet`, params);
};

/**
 * 重置Rescue用户密码
 * @param params 用户ID
 * @returns 重置结果
 */
export const resetRescueUserPassword = (params: { id: number }) => {
  const { id } = params;
  return http.put(RESCUE_MODULE + `/rescue-user/reset/password/${id}`, {});
};

/**
 * 解锁Rescue用户
 * @param params 用户ID数组
 * @returns 解锁结果
 */
export const unlockRescueUser = (params: { ids: (string | number)[] }) => {
  return http.post(RESCUE_MODULE + `/rescue-user/unlock`, params);
};
