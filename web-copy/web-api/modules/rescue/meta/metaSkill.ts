import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IMetaSkill } from '@/api/interface/rescue/meta/metaSkill'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getMetaSkillListApi = (params: IMetaSkill.Query) => {
  return http.get<IPage<IMetaSkill.Row>>(RESCUE_MODULE + `/meta-skill/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createMetaSkillApi = (params: IMetaSkill.Form) => {
  return http.post(RESCUE_MODULE + `/meta-skill/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateMetaSkillApi = (params: IMetaSkill.Form) => {
  return http.put(RESCUE_MODULE + `/meta-skill/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeMetaSkillApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/meta-skill/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getMetaSkillDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IMetaSkill.Row>(RESCUE_MODULE + `/meta-skill/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importMetaSkillExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/meta-skill/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportMetaSkillExcelApi  = (params: IMetaSkill.Query) => {
  return http.download(RESCUE_MODULE + `/meta-skill/export`, params)
}
