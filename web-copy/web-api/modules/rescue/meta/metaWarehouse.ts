import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IMetaWarehouse } from 'src/api/interface/rescue/meta/metaWarehouse'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getMetaWarehouseListApi = (params: IMetaWarehouse.Query) => {
  return http.get<IPage<IMetaWarehouse.Row>>(RESCUE_MODULE + `/meta-warehouse/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createMetaWarehouseApi = (params: IMetaWarehouse.Form) => {
  return http.post(RESCUE_MODULE + `/meta-warehouse/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateMetaWarehouseApi = (params: IMetaWarehouse.Form) => {
  return http.put(RESCUE_MODULE + `/meta-warehouse/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeMetaWarehouseApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/meta-warehouse/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getMetaWarehouseDetailApi = (params: { id: number }) => {
  return http.get<IMetaWarehouse.Row>(RESCUE_MODULE + `/meta-warehouse/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importMetaWarehouseExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/meta-warehouse/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportMetaWarehouseExcelApi  = (params: IMetaWarehouse.Query) => {
  return http.download(RESCUE_MODULE + `/meta-warehouse/export`, params)
}
