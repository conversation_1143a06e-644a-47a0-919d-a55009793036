import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWork } from '@/api/interface/rescue/work/work'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkListApi = (params: IWork.Query) => {
  return http.get<IPage<IWork.Row>>(RESCUE_MODULE + `/work/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkApi = (params: IWork.Form) => {
  return http.post(RESCUE_MODULE + `/work/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkApi = (params: IWork.Form) => {
  return http.put(RESCUE_MODULE + `/work/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWork.Row>(RESCUE_MODULE + `/work/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkExcelApi  = (params: IWork.Query) => {
  return http.download(RESCUE_MODULE + `/work/export`, params)
}
