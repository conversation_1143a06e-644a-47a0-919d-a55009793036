import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWorkUserApply } from '@/api/interface/rescue/work/workUserApply'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkUserApplyListApi = (params: IWorkUserApply.Query) => {
  return http.get<IPage<IWorkUserApply.Row>>(RESCUE_MODULE + `/work-user-apply/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkUserApplyApi = (params: IWorkUserApply.Form) => {
  return http.post(RESCUE_MODULE + `/work-user-apply/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkUserApplyApi = (params: IWorkUserApply.Form) => {
  return http.put(RESCUE_MODULE + `/work-user-apply/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkUserApplyApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work-user-apply/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkUserApplyDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWorkUserApply.Row>(RESCUE_MODULE + `/work-user-apply/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkUserApplyExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work-user-apply/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkUserApplyExcelApi  = (params: IWorkUserApply.Query) => {
  return http.download(RESCUE_MODULE + `/work-user-apply/export`, params)
}

/**
* 新增报名申请
* @param params
* @returns {*}
*/
export const applyWorkUserApi = (params: IWorkUserApply.ApplyForm) => {
  return http.post(RESCUE_MODULE + `/work-user-apply/create`, params)
}

/**
* 审批报名申请
* @param params
* @returns {*}
*/
export const reviewWorkUserApplyApi = (params: IWorkUserApply.ReviewForm) => {
  return http.put(RESCUE_MODULE + `/work-user-apply/review`, params)
}
