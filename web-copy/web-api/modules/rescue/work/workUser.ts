import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWorkUser } from '@/api/interface/rescue/work/workUser'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkUserListApi = (params: IWorkUser.Query) => {
  return http.get<IPage<IWorkUser.Row>>(RESCUE_MODULE + `/work-user/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkUserApi = (params: IWorkUser.Form) => {
  return http.post(RESCUE_MODULE + `/work-user/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkUserApi = (params: IWorkUser.Form) => {
  return http.put(RESCUE_MODULE + `/work-user/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkUserApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work-user/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkUserDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWorkUser.Row>(RESCUE_MODULE + `/work-user/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkUserExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work-user/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkUserExcelApi  = (params: IWorkUser.Query) => {
  return http.download(RESCUE_MODULE + `/work-user/export`, params)
}
