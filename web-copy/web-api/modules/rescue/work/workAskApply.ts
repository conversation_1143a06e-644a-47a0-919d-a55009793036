import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWorkAskApply } from '@/api/interface/rescue/work/workAskApply'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkAskApplyListApi = (params: IWorkAskApply.Query) => {
  return http.get<IPage<IWorkAskApply.Row>>(RESCUE_MODULE + `/work-ask-apply/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkAskApplyApi = (params: IWorkAskApply.Form) => {
  return http.post(RESCUE_MODULE + `/work-ask-apply/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkAskApplyApi = (params: IWorkAskApply.Form) => {
  return http.put(RESCUE_MODULE + `/work-ask-apply/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkAskApplyApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work-ask-apply/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkAskApplyDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWorkAskApply.Row>(RESCUE_MODULE + `/work-ask-apply/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkAskApplyExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work-ask-apply/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkAskApplyExcelApi  = (params: IWorkAskApply.Query) => {
  return http.download(RESCUE_MODULE + `/work-ask-apply/export`, params)
}

/**
* 审批求助信息
* @param params
* @returns {*}
*/
export const reviewWorkAskApplyApi = (params: IWorkAskApply.ReviewForm) => {
  return http.put(RESCUE_MODULE + `/work-ask-apply/review`, params)
}
