import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWorkEvent } from '@/api/interface/rescue/work/workEvent'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkEventListApi = (params: IWorkEvent.Query) => {
  return http.get<IPage<IWorkEvent.Row>>(RESCUE_MODULE + `/work-event/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkEventApi = (params: IWorkEvent.Form) => {
  return http.post(RESCUE_MODULE + `/work-event/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkEventApi = (params: IWorkEvent.Form) => {
  return http.put(RESCUE_MODULE + `/work-event/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkEventApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work-event/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkEventDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWorkEvent.Row>(RESCUE_MODULE + `/work-event/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkEventExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work-event/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkEventExcelApi  = (params: IWorkEvent.Query) => {
  return http.download(RESCUE_MODULE + `/work-event/export`, params)
}

/**
* 创建事件记录
* @param params
* @returns {*}
*/
export const createWorkEventRecordApi = (params: IWorkEvent.CreateForm) => {
  return http.post(RESCUE_MODULE + `/work-event/create`, params)
}
