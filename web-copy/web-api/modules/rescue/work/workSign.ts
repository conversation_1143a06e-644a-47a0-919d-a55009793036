import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IWorkSign } from '@/api/interface/rescue/work/workSign'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getWorkSignListApi = (params: IWorkSign.Query) => {
  return http.get<IPage<IWorkSign.Row>>(RESCUE_MODULE + `/work-sign/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createWorkSignApi = (params: IWorkSign.Form) => {
  return http.post(RESCUE_MODULE + `/work-sign/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateWorkSignApi = (params: IWorkSign.Form) => {
  return http.put(RESCUE_MODULE + `/work-sign/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeWorkSignApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/work-sign/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getWorkSignDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IWorkSign.Row>(RESCUE_MODULE + `/work-sign/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importWorkSignExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/work-sign/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportWorkSignExcelApi  = (params: IWorkSign.Query) => {
  return http.download(RESCUE_MODULE + `/work-sign/export`, params)
}

/**
* 签到
* @param params
* @returns {*}
*/
export const workSignApi = (params: IWorkSign.SignForm) => {
  return http.post(RESCUE_MODULE + `/work-sign/sign`, params)
}
