import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IOrderReview } from 'src/api/interface/rescue/order/orderReview'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getOrderReviewListApi = (params: IOrderReview.Query) => {
  return http.get<IPage<IOrderReview.Row>>(RESCUE_MODULE + `/order-review/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createOrderReviewApi = (params: IOrderReview.Form) => {
  return http.post(RESCUE_MODULE + `/order-review/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateOrderReviewApi = (params: IOrderReview.Form) => {
  return http.put(RESCUE_MODULE + `/order-review/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeOrderReviewApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/order-review/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getOrderReviewDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IOrderReview.Row>(RESCUE_MODULE + `/order-review/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importOrderReviewExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/order-review/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportOrderReviewExcelApi  = (params: IOrderReview.Query) => {
  return http.download(RESCUE_MODULE + `/order-review/export`, params)
}
