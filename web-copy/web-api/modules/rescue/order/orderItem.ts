import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IOrderItem } from 'src/api/interface/rescue/order/orderItem'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getOrderItemListApi = (params: IOrderItem.Query) => {
  return http.get<IPage<IOrderItem.Row>>(RESCUE_MODULE + `/order-item/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createOrderItemApi = (params: IOrderItem.Form) => {
  return http.post(RESCUE_MODULE + `/order-item/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateOrderItemApi = (params: IOrderItem.Form) => {
  return http.put(RESCUE_MODULE + `/order-item/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeOrderItemApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/order-item/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getOrderItemDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IOrderItem.Row>(RESCUE_MODULE + `/order-item/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importOrderItemExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/order-item/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportOrderItemExcelApi  = (params: IOrderItem.Query) => {
  return http.download(RESCUE_MODULE + `/order-item/export`, params)
}
