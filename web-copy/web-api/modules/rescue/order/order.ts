import http from '@/api'
import { RESCUE_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IOrder } from 'src/api/interface/rescue/order/order'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getOrderListApi = (params: IOrder.Query) => {
  return http.get<IPage<IOrder.Row>>(RESCUE_MODULE + `/order/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createOrderApi = (params: IOrder.OrderCreateDTO) => {
  return http.post(RESCUE_MODULE + `/order/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateOrderApi = (params: IOrder.Form) => {
  return http.put(RESCUE_MODULE + `/order/update`, params)
}

/**
* 审批订单
* @param params
* @returns {*}
*/
export const reviewOrderApi = (params: IOrder.OrderReviewDTO) => {
  return http.put(RESCUE_MODULE + `/order/review`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeOrderApi = (params: { ids: (string | number)[] }) => {
 return http.delete(RESCUE_MODULE + `/order/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getOrderDetailApi = (params: { id: number }) => {
  return http.get<IOrder.Row>(RESCUE_MODULE + `/order/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importOrderExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(RESCUE_MODULE + `/order/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportOrderExcelApi  = (params: IOrder.Query) => {
  return http.download(RESCUE_MODULE + `/order/export`, params)
}
