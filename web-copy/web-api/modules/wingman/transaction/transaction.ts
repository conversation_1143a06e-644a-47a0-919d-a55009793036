import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { ITransaction } from '@/api/interface/wingman/transaction/transaction'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getTransactionListApi = (params: ITransaction.Query) => {
  return http.get<IPage<ITransaction.Row>>(WINGMAN_MODULE + `/transaction/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createTransactionApi = (params: ITransaction.Form) => {
  return http.post(WINGMAN_MODULE + `/transaction/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateTransactionApi = (params: ITransaction.Form) => {
  return http.put(WINGMAN_MODULE + `/transaction/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeTransactionApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/transaction/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getTransactionDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<ITransaction.Row>(WINGMAN_MODULE + `/transaction/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importTransactionExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/transaction/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportTransactionExcelApi  = (params: ITransaction.Query) => {
  return http.download(WINGMAN_MODULE + `/transaction/export`, params)
}
