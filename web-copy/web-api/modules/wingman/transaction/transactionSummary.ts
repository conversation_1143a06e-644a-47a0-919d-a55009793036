import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { ITransactionSummary } from '@/api/interface/wingman/transaction/transactionSummary'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getTransactionSummaryListApi = (params: ITransactionSummary.Query) => {
  return http.get<IPage<ITransactionSummary.Row>>(WINGMAN_MODULE + `/transaction-summary/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createTransactionSummaryApi = (params: ITransactionSummary.Form) => {
  return http.post(WINGMAN_MODULE + `/transaction-summary/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateTransactionSummaryApi = (params: ITransactionSummary.Form) => {
  return http.put(WINGMAN_MODULE + `/transaction-summary/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeTransactionSummaryApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/transaction-summary/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getTransactionSummaryDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<ITransactionSummary.Row>(WINGMAN_MODULE + `/transaction-summary/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importTransactionSummaryExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/transaction-summary/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportTransactionSummaryExcelApi  = (params: ITransactionSummary.Query) => {
  return http.download(WINGMAN_MODULE + `/transaction-summary/export`, params)
}
