import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IOrder } from '@/api/interface/wingman/order/order'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getOrderListApi = (params: IOrder.Query) => {
  return http.get<IPage<IOrder.Row>>(WINGMAN_MODULE + `/order/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createOrderApi = (params: IOrder.Form) => {
  return http.post(WINGMAN_MODULE + `/order/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateOrderApi = (params: IOrder.Form) => {
  return http.put(WINGMAN_MODULE + `/order/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeOrderApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/order/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getOrderDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IOrder.Row>(WINGMAN_MODULE + `/order/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importOrderExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/order/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportOrderExcelApi  = (params: IOrder.Query) => {
  return http.download(WINGMAN_MODULE + `/order/export`, params)
}
