import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IProduct } from '@/api/interface/wingman/product/product'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getProductListApi = (params: IProduct.Query) => {
  return http.post<IPage<IProduct.Row>>(WINGMAN_MODULE + `/product/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createProductApi = (params: IProduct.Form) => {
  return http.post(WINGMAN_MODULE + `/product/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateProductApi = (params: IProduct.Form) => {
  return http.put(WINGMAN_MODULE + `/product/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeProductApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/product/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getProductDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IProduct.Row>(WINGMAN_MODULE + `/product/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importProductExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/product/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportProductExcelApi  = (params: IProduct.Query) => {
  return http.download(WINGMAN_MODULE + `/product/export`, params)
}
