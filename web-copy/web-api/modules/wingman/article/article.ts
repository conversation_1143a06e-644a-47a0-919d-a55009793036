import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { IArticle } from '@/api/interface/wingman/article/article'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getArticleListApi = (params: IArticle.Query) => {
  return http.post<IPage<IArticle.Row>>(WINGMAN_MODULE + `/article/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createArticleApi = (params: IArticle.Form) => {
  return http.post(WINGMAN_MODULE + `/article/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateArticleApi = (params: IArticle.Form) => {
  return http.put(WINGMAN_MODULE + `/article/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeArticleApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/article/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getArticleDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<IArticle.Row>(WINGMAN_MODULE + `/article/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importArticleExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/article/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportArticleExcelApi  = (params: IArticle.Query) => {
  return http.download(WINGMAN_MODULE + `/article/export`, params)
}
