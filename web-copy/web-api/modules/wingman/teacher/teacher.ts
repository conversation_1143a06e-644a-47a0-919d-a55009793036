import http from '@/api'
import { WINGMAN_MODULE } from 'src/api/helper/prefix'
import type { IPage } from 'src/api/interface'
import type { ITeacher } from '@/api/interface/wingman/teacher/teacher'
import type { UploadRawFile } from "element-plus/es/components/upload/src/upload";
import type { AxiosRequestConfig } from 'axios';

/**
* 查询列表
* @param params
* @returns {*}
*/
export const getTeacherListApi = (params: ITeacher.Query) => {
  return http.get<IPage<ITeacher.Row>>(WINGMAN_MODULE + `/teacher/list`, params)
}

/**
* 添加
* @param params
* @returns {*}
*/
export const createTeacherApi = (params: ITeacher.Form) => {
  return http.post(WINGMAN_MODULE + `/teacher/create`, params)
}

/**
* 修改
* @param params
* @returns {*}
*/
export const updateTeacherApi = (params: ITeacher.Form) => {
  return http.put(WINGMAN_MODULE + `/teacher/update`, params)
}

/**
* 删除
* @param params
* @returns {*}
*/
export const removeTeacherApi = (params: { ids: (string | number)[] }) => {
 return http.delete(WINGMAN_MODULE + `/teacher/remove`, params)
}

/**
* 获取详情
* @param params
* @returns {*}
*/
export const getTeacherDetailApi = (params: { id: number }) => {
  const { id } = params
  return http.get<ITeacher.Row>(WINGMAN_MODULE + `/teacher/detail`, params)
}

/**
* 导入excel
* @param params
*/
export const importTeacherExcelApi = (params : UploadRawFile, config?: AxiosRequestConfig<{}> | undefined) => {
  return http.upload(WINGMAN_MODULE + `/teacher/import`, params, config)
}

/**
* 导出excel
* @param params
* @returns {*}
*/
export const exportTeacherExcelApi  = (params: ITeacher.Query) => {
  return http.download(WINGMAN_MODULE + `/teacher/export`, params)
}
