import type { IPageQuery } from 'src/api/interface'
import type { IRole } from '@/api/interface/system/admin/role'

export namespace IUserInfo {

  // 查询条件
  export interface Query extends IPageQuery {
    userId?: number
    deptId?: number
    name?: string
    nickName?: string
    numberPrefix?: string
    number?: number
    post?: string
    avatar?: string
    phone?: string
    identityCard?: string
    identityStartDateStart?: string
    identityStartDateEnd?: string
    identityEndDateStart?: string
    identityEndDateEnd?: string
    passportNumber?: string
    insuranceStatus?: string
    politicsStatus?: string
    bloodType?: string
    sex?: string
    birthdayStart?: string
    birthdayEnd?: string
    signatureImage?: string
    identityImage?: string
    informationImage?: string
    totalDutyDuration?: number
    yearDutyDuration?: number
    emergencyContact?: string
    emergencyContactPhone?: string
    medicalHistory?: string
    allergiesHistory?: string
    enable?: string
    approveTimeStart?: string
    approveTimeEnd?: string
    tenantId?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    userId?: number
    deptId?: number
    name?: string
    nickName?: string
    numberPrefix?: string
    number?: number
    post?: string
    avatar?: string
    phone?: string
    identityCard?: string
    identityStartDate?: string
    identityEndDate?: string
    passportNumber?: string
    insuranceStatus?: string
    politicsStatus?: string
    bloodType?: string
    sex?: string
    birthday?: string
    remark?: string
    signatureImage?: string
    identityImage?: string
    informationImage?: string
    totalDutyDuration?: number
    yearDutyDuration?: number
    emergencyContact?: string
    emergencyContactPhone?: string
    medicalHistory?: string
    allergiesHistory?: string
    enable?: string
    approveTime?: string
    tenantId?: string
    roleIds?: number[]
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    userId?: number
    deptId?: number
    deptName?: string
    name?: string
    nickName?: string
    numberPrefix?: string
    number?: number
    post?: string
    avatar?: string
    phone?: string
    identityCard?: string
    identityStartDate?: string
    identityEndDate?: string
    passportNumber?: string
    insuranceStatus?: string
    politicsStatus?: string
    bloodType?: string
    sex?: string
    birthday?: string
    remark?: string
    signatureImage?: string
    identityImage?: string
    informationImage?: string
    totalDutyDuration?: number
    yearDutyDuration?: number
    emergencyContact?: string
    emergencyContactPhone?: string
    medicalHistory?: string
    allergiesHistory?: string
    enable?: string
    approveTime?: string
    tenantId?: string
    roles?: IRole.Info[]
    roleIds?: number[]
  }

}
