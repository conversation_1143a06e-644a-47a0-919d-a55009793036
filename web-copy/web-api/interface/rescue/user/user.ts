// Rescue用户模块接口定义
import type { IPageQuery } from 'src/api/interface';

export namespace IRescueUser {
  // 查询参数
  export interface Query extends IPageQuery {
    username?: string;
    nickname?: string;
    name?: string;
    phone?: string;
    email?: string;
    accountStatusCd?: string;
    deptId?: number;
    isThisDeep?: boolean;
    startDate?: string;
    endDate?: string;
  }

  // Rescue用户表单数据
  export interface Form {
    id?: number;
    username?: string;
    nickname?: string;
    name?: string;
    phone?: string;
    email?: string;
    sex?: number;
    birthday?: string;
    idCard?: string;
    logo?: string;
    accountStatusCd?: string;
    userTagCd?: string;
    // Rescue特有字段
    emergencyContactName?: string;    // 紧急联系人姓名
    emergencyContactPhone?: string;   // 紧急联系人电话
    specialSkills?: string;           // 专业技能
    certifications?: string;          // 资质证书
    experienceYears?: number;         // 工作经验年数
    availabilityStatus?: string;      // 可用状态
  }

  // Rescue用户信息
  export interface Info {
    id: number;
    username: string;
    nickname: string;
    name: string;
    phone?: string;
    email?: string;
    sex?: number;
    birthday?: string;
    idCard?: string;
    logo?: string;
    accountStatusCd?: string;
    userTagCd?: string;
    // Rescue特有字段
    emergencyContactName?: string;
    emergencyContactPhone?: string;
    specialSkills?: string;
    certifications?: string;
    experienceYears?: number;
    availabilityStatus?: string;
    // 系统字段
    createTime: string;
    updateTime: string;
    delFlag?: string;
    // 关联信息
    deptInfo?: string;
    roleInfo?: string;
  }

  // 用户选项（用于选择器）
  export interface Options {
    id: number;
    username: string;
    nickname: string;
    name: string;
    logo?: string;
  }

  // 批量操作参数
  export interface BatchOperation {
    userIds: number[];
    operation: 'delete' | 'enable' | 'disable';
    data?: any;
  }
}
