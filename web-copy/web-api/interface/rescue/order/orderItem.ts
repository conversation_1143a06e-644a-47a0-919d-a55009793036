import type { IPageQuery } from 'src/api/interface'

export namespace IOrderItem {

  // 查询条件
  export interface Query extends IPageQuery {
    orderId?: number
    tenantId?: string
    deptId?: number
    productId?: number
    productName?: string
    image?: string
    skuId?: number
    skuName?: string
    skuImage?: string
    quantity?: number
    price?: number
  }

  // 编辑form表单
  export interface Form {
    id?: number
    orderId?: number
    tenantId?: string
    deptId?: number
    productId?: number
    productName?: string
    image?: string
    skuId?: number
    skuName?: string
    skuImage?: string
    quantity?: number
    price?: number
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    orderId?: number
    tenantId?: string
    deptId?: number
    productId?: number
    productName?: string
    image?: string
    skuId?: number
    skuName?: string
    skuImage?: string
    quantity?: number
    price?: number
  }

}
