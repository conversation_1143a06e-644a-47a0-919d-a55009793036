import type { IPageQuery } from 'src/api/interface'
import type { IOrderItem } from './orderItem'
import type { IOrderReview } from './orderReview'

export namespace IOrder {

  // SKU数量信息
  export interface SkuQuantity {
    skuId: number
    quantity: number
  }

  // 订单创建DTO
  export interface OrderCreateDTO {
    workNumber?: string
    type: string
    reason: string
    operate: boolean
    remark?: string
    skuQuantityList: SkuQuantity[]
  }

  // 订单审批DTO
  export interface OrderReviewDTO {
    id: number
    reviewOpinion?: string
    status?: string
    remark?: string
  }

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    workId?: number
    workNumber?: string
    orderNumber?: string
    type?: string
    reason?: string
    applyUserId?: number
    applyName?: string
    applyPhone?: string
    operate?: string
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    reviewOpinion?: string
    totalPrice?: number
    totalAmount?: number
    status?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    workNumber?: string
    orderNumber?: string

    type?: string
    reason?: string
    applyUserId?: number
    applyName?: string
    applyPhone?: string
    operate?: string
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    reviewOpinion?: string
    totalPrice?: number
    totalAmount?: number
    status?: string
    remark?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    workNumber?: string
    orderNumber?: string
    type?: string
    reason?: string
    applyUserId?: number
    applyName?: string
    applyPhone?: string
    operate?: string
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    reviewOpinion?: string
    totalPrice?: number
    totalAmount?: number
    status?: string
    remark?: string
    orderItems?: IOrderItem.Row[]
    orderReviews?: IOrderReview.Row[]
  }

}
