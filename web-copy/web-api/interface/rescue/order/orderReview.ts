import type { IPageQuery } from 'src/api/interface'

export namespace IOrderReview {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    orderId?: number
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    originStatus?: string
    targetStatus?: string
    reviewOpinion?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    orderId?: number
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    originStatus?: string
    targetStatus?: string
    reviewOpinion?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    orderId?: number
    reviewUserId?: number
    reviewName?: string
    reviewPhone?: string
    originStatus?: string
    targetStatus?: string
    reviewOpinion?: string
  }

}
