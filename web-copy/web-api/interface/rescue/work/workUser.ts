import type { IPageQuery } from 'src/api/interface'

export namespace IWorkUser {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    name?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    name?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    name?: string
  }

}
