import type { IPageQuery } from 'src/api/interface'
import type { IWork } from './work'

export namespace IWorkUserApply {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    status?: string
    name?: string
    applyTimeStart?: string
    applyTimeEnd?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    status?: string
    name?: string
    applyTime?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    status?: string
    name?: string
    applyTime?: string
    work?: IWork.Row
  }

  // 报名申请表单
  export interface ApplyForm {
    workId?: number
    applyTime?: string
  }

  // 审批表单
  export interface ReviewForm {
    id: number
    reviewStatus: string
  }

}
