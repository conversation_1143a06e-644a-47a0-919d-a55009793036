import type { IPageQuery } from 'src/api/interface'
import type { IWorkEvent } from './workEvent'
import type { IWorkUser } from './workUser'
import type { IWorkSign } from './workSign'

export namespace IWork {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    categoryId?: number
    regionId?: number
    workNumber?: string
    title?: string
    content?: string
    applyRequire?: string
    principalUserId?: number
    principalName?: string
    principalPhone?: string
    askName?: string
    askContact?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    enable?: string
    status?: string
    enableCheckAddress?: string
    enableCheckPassword?: string
    enableCheckTime?: string
    signOnPassword?: string
    signExitPassword?: string
    startTimeStart?: string
    startTimeEnd?: string
    endTimeStart?: string
    endTimeEnd?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    categoryId?: number
    regionId?: number
    workNumber?: string
    title?: string
    content?: string
    applyRequire?: string
    principalUserId?: number
    principalName?: string
    principalPhone?: string
    askName?: string
    askContact?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    remark?: string
    enable?: boolean
    status?: string
    enableCheckAddress?: boolean
    enableCheckPassword?: boolean
    enableCheckTime?: boolean
    signOnPassword?: string
    signExitPassword?: string
    startTime?: string
    endTime?: string
    userIds?: number[]
    events?: IWorkEvent.Form[]
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    categoryId?: number
    regionId?: number
    workNumber?: string
    title?: string
    content?: string
    applyRequire?: string
    principalUserId?: number
    principalName?: string
    principalPhone?: string
    askName?: string
    askContact?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    remark?: string
    enable?: boolean
    status?: string
    enableCheckAddress?: boolean
    enableCheckPassword?: boolean
    enableCheckTime?: boolean
    signOnPassword?: string
    signExitPassword?: string
    startTime?: string
    endTime?: string
    workUsers?: IWorkUser.Row[]
    workEvents?: IWorkEvent.Row[]
    workSigns?: IWorkSign.Row[]
  }

}
