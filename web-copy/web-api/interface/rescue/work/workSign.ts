import type { IPageQuery } from 'src/api/interface'

export namespace IWorkSign {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    signType?: string
    name?: string
    signDateStart?: string
    signDateEnd?: string
    signTimeStart?: string
    signTimeEnd?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    signType?: string
    name?: string
    signDate?: string
    signTime?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    workId?: number
    userId?: number
    signType?: string
    name?: string
    signDate?: string
    signTime?: string
  }

  // 签到表单
  export interface SignForm {
    workId?: number
    signType?: string
    signOnPassword?: string
    signExitPassword?: string
    longitude?: string
    latitude?: string
  }

}
