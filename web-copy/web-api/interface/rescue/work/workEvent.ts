import type { IPageQuery } from 'src/api/interface'

export namespace IWorkEvent {

  // 查询条件
  export interface Query extends IPageQuery {
    workId?: number
    tenantId?: string
    deptId?: number
    userId?: number
    title?: string
    content?: string
    image?: string
    eventTimeStart?: string
    eventTimeEnd?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    workId?: number
    tenantId?: string
    deptId?: number
    userId?: number
    title?: string
    content?: string
    image?: string
    eventTime?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    workId?: number
    tenantId?: string
    deptId?: number
    userId?: number
    title?: string
    content?: string
    image?: string
    eventTime?: string
  }

  // 创建事件记录表单
  export interface CreateForm {
    workId?: number
    title?: string
    content?: string
    image?: string
    eventTime?: string
  }

}
