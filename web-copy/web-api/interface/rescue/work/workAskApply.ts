import type { IPageQuery } from 'src/api/interface'

export namespace IWorkAskApply {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    reviewUserId?: number
    regionId?: number
    askContent?: string
    askPhone?: string
    askName?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    status?: string
    applyTimeStart?: string
    applyTimeEnd?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    reviewUserId?: number
    regionId?: number
    askContent?: string
    askPhone?: string
    askName?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    remark?: string
    status?: string
    applyTime?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    reviewUserId?: number
    regionId?: number
    askContent?: string
    askPhone?: string
    askName?: string
    address?: string
    detailAddress?: string
    longitude?: number
    latitude?: number
    remark?: string
    status?: string
    applyTime?: string
    createTime?: string
    updateTime?: string
  }

  // 审批表单
  export interface ReviewForm {
    id: number
    reviewStatus: string
    title?: string
    deptId?: number
    categoryId?: number
    regionId?: number
    workNumber?: string
    applyRequire?: string
    principalUserId?: number
    principalName?: string
    principalPhone?: string
    enableCheckAddress?: boolean
    enableCheckPassword?: boolean
    enableCheckTime?: boolean
    signOnPassword?: string
    signExitPassword?: string
    startTime?: string
    endTime?: string
  }

}
