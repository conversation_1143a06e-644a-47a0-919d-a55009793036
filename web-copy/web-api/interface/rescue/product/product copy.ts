import type { IPageQuery } from 'src/api/interface'

export namespace IProduct {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    categoryId?: number
    warehouseId?: number
    name?: string
    image?: string
    productNumber?: string
    enable?: string
    tagIds?: number[]
  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    categoryId?: number
    warehouseId?: number
    name?: string
    image?: string
    productNumber?: string
    enable?: boolean
    tagIds?: number[]
    productSkus?: ProductSkuInsideDTO[]

  }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    categoryId?: number
    warehouseId?: number
    name?: string
    image?: string
    productNumber?: string
    enable?: boolean
    tags?: any[]
    productSkus?: any[]
    productLots?: any[]
  }

  // 物资选择器返回结构
  export interface ProductSelectVO {
    id?: number
    name?: string
    image?: string
    productNumber?: string
    enable?: boolean
    productSkus?: ProductSkuSelectVO[]
  }

  // 物资规格选择器返回结构
  export interface ProductSkuSelectVO {
    id?: number
    productId?: number
    name?: string
    image?: string
    stock?: number
    price?: number
    enable?: boolean
  }

  // 物资创建DTO
  export interface ProductCreateDTO {
    categoryId?: number
    warehouseId?: number
    name: string
    image?: string
    productNumber?: string
    enable?: boolean
    tagIds?: number[]
    productSkus: ProductSkuInsideDTO[]
  }

  // 物资规格DTO
  export interface ProductSkuInsideDTO {
    id?: number // 规格ID，更新时需要
    name?: string // 规格名称
    image?: string // 规格图片
    stock?: number
    price?: number
    enable?: boolean
    productLots?: ProductLotInsideDTO[]
  }

  // 物资批次DTO
  export interface ProductLotInsideDTO {
    id?: number // 批次ID，更新时需要
    skuId?: number
    lotNumber?: string
    title?: string
    stock?: number
    price?: number
    quantity?: number
    storageDate?: string | undefined
    productDate?: string | undefined
    discardDate?: string | undefined
    overhaulLastDate?: string | undefined
    overhaulGap?: number
    expirationTime?: string | undefined // 过期时间
    overhaulNextDate?: string | undefined // 下次检修时间
  }
}
