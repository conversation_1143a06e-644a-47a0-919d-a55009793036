import type { IPageQuery } from 'src/api/interface'

export namespace IProductLot {

  // 查询条件
  export interface Query extends IPageQuery {
    tenantId?: string
    deptId?: number
    productId?: number
    skuId?: number
    lotNumber?: string
    title?: string
    price?: number
    quantity?: number
    storageDateStart?: string
    storageDateEnd?: string
    productDateStart?: string
    productDateEnd?: string
    expirationTimeStart?: string
    expirationTimeEnd?: string
    discardDateStart?: string
    discardDateEnd?: string
    overhaulLastDateStart?: string
    overhaulLastDateEnd?: string
    overhaulNextDateStart?: string
    overhaulNextDateEnd?: string
    overhaulGap?: number

  }

  // 编辑form表单
  export interface Form {
    id?: number
    tenantId?: string
    deptId?: number
    productId?: number
    skuId?: number
    lotNumber?: string
    title?: string
    price?: number
    quantity?: number
    storageDate?: string
    productDate?: string
    expirationTime?: string
    discardDate?: string
    overhaulLastDate?: string
    overhaulNextDate?: string
    overhaulGap?: number
    productName?: string
    skuName?: string
 }

 // 检修form表单
 export interface OverhaulForm {
   lotIds: number[]
   overhaulNextDate?: string
   overhaulDate?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    tenantId?: string
    deptId?: number
    productId?: number
    skuId?: number
    lotNumber?: string
    title?: string
    price?: number
    quantity?: number
    storageDate?: string | undefined
    productDate?: string | undefined
    expirationTime?: string | undefined
    discardDate?: string | undefined
    overhaulLastDate?: string | undefined
    overhaulNextDate?: string | undefined
    overhaulGap?: number
    productName?: string
    skuName?: string
  }

}
