import type { IPageQuery } from 'src/api/interface'

export namespace IProductBundle {

  // 查询条件
  export interface Query extends IPageQuery {
    productId?: number
    tenantId?: string
    deptId?: number
    skuId?: number
    quantity?: number
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    productId?: number
    tenantId?: string
    deptId?: number
    skuId?: number
    quantity?: number
    enable?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    productId?: number
    tenantId?: string
    deptId?: number
    skuId?: number
    quantity?: number
    enable?: string
  }

}
