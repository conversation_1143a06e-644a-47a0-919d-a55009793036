import type { IPageQuery } from 'src/api/interface'

export namespace IMetaWarehouse {

  // 查询条件
  export interface Query extends IPageQuery {
    regionId?: number
    tenantId?: string
    deptId?: number
    common?: string
    name?: string
    image?: string
    address?: string
    detailAddress?: string
    location?: string
    longitude?: string
    latitude?: string
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    regionId?: number
    tenantId?: string
    deptId?: number
    common?: string
    name?: string
    image?: string
    address?: string
    detailAddress?: string
    location?: string
    longitude?: string
    latitude?: string
    enable?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    regionId?: number
    tenantId?: string
    deptId?: number
    common?: string
    name?: string
    image?: string
    address?: string
    detailAddress?: string
    location?: string
    longitude?: string
    latitude?: string
    enable?: string
  }

}
