import type { IPageQuery } from 'src/api/interface'

export namespace IArticle {

  // 查询条件
  export interface Query extends IPageQuery {
    userId?: number
    expertUserId?: number
    categoryId?: number
    articleUser?: number
    title?: string
    price?: number
    content?: string
    achievement?: string
    image?: string
    imageGallery?: string
    expertContact?: string
    expertQrCode?: string
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    userId?: number
    expertUserId?: number
    categoryId?: number
    articleUser?: number
    title?: string
    price?: number
    content?: string
    achievement?: string
    image?: string
    imageGallery?: string
    expertContact?: string
    expertQrCode?: string
    enable?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    userId?: number
    expertUserId?: number
    categoryId?: number
    articleUser?: number
    title?: string
    price?: number
    content?: string
    achievement?: string
    image?: string
    imageGallery?: string
    expertContact?: string
    expertQrCode?: string
    enable?: string
  }

}
