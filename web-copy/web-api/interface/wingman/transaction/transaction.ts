import type { IPageQuery } from 'src/api/interface'

export namespace ITransaction {

  // 查询条件
  export interface Query extends IPageQuery {
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    amount?: number
    timeStart?: string
    timeEnd?: string
    type?: string
    handleUser?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    amount?: number
    time?: string
    type?: string
    handleUser?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    remark?: string
    enable?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    amount?: number
    time?: string
    type?: string
    handleUser?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    remark?: string
    enable?: string
  }

}
