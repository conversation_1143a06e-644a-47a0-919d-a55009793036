import type { IPageQuery } from 'src/api/interface'

export namespace ITransactionSummary {

  // 查询条件
  export interface Query extends IPageQuery {
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    orderAmount?: number
    payoutAmount?: number
    receiveAmount?: number
    amount?: number
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    orderAmount?: number
    payoutAmount?: number
    receiveAmount?: number
    amount?: number
    remark?: string
    enable?: string
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    orderId?: number
    orderNumber?: string
    transactionNumber?: string
    orderAmount?: number
    payoutAmount?: number
    receiveAmount?: number
    amount?: number
    remark?: string
    enable?: string
  }

}
