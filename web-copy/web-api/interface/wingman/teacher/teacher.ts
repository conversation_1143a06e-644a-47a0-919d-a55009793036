import type { IPageQuery } from 'src/api/interface'
import type { IMetaTag } from 'src/api/interface/system/meta/metaTag'

export namespace ITeacher {

  // 查询条件
  export interface Query extends IPageQuery {
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    simpleContent?: string
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    contentImage?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
    tenantId?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    simpleContent?: string
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    contentImage?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
    tenantId?: string
    tagIds?: number[]
 }

  // list或detail返回结构
  export interface Row {
    id?: number
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    simpleContent?: string
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    contentImage?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
    tenantId?: string
    tagIds?: number[]
    tagList?: IMetaTag.Row[]
  }

}
