import type { IPageQuery } from 'src/api/interface'

export namespace IOrder {

  // 查询条件
  export interface Query extends IPageQuery {
    userId?: number
    productId?: number
    productName?: string
    productType?: string
    transactionId?: string
    orderNumber?: string
    orderUser?: string
    orderPhone?: string
    name?: string
    price?: number
    paymentAmount?: number
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    serviceTime?: string
    status?: string
    enable?: string
    bindCode?: string
    bindUserId?: number
    bindName?: string
    bindPhone?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    userId?: number
    productId?: number
    productName?: string
    productType?: string
    transactionId?: string
    orderNumber?: string
    orderUser?: string
    orderPhone?: string
    name?: string
    price?: number
    paymentAmount?: number
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    serviceTime?: string
    status?: string
    enable?: string
    createTime?: string
    bindCode?: string
    bindUserId?: number
    bindName?: string
    bindPhone?: string
    remark?: string

  }

  // list或detail返回结构
  export interface Row {
    id?: number
    userId?: number
    productId?: number
    productName?: string
    productType?: string
    transactionId?: string
    orderNumber?: string
    orderUser?: string
    orderPhone?: string
    name?: string
    price?: number
    paymentAmount?: number
    content?: string
    type?: string
    inventory?: number
    quantity?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertQrCode?: string
    serviceTime?: string
    status?: string
    enable?: string
    createTime?: string
    bindCode?: string
    bindUserId?: number
    bindName?: string
    bindPhone?: string
    remark?: string

  }

}
