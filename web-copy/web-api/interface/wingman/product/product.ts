import type { IPageQuery } from 'src/api/interface'
import type { IMetaTag } from 'src/api/interface/system/meta/metaTag'

export namespace IProduct {

  // 查询条件
  export interface Query extends IPageQuery {
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    content?: string
    contentImage?: string
    type?: string
    inventory?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
  }

  // 编辑form表单
  export interface Form {
    id?: number
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    content?: string
    contentImage?: string
    type?: string
    inventory?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
    tagIds?: number[]
  }

  // list或detail返回结构
  export interface Row {
    id?: number
    categoryId?: number
    name?: string
    subName?: string
    price?: number
    content?: string
    contentImage?: string
    type?: string
    inventory?: number
    image?: string
    imageGallery?: string
    expertUserId?: number
    expertName?: string
    expertContact?: string
    expertUrl?: string
    expertQrCode?: string
    serviceTime?: string
    enable?: string
    tagIds?: number[]
    tagList?: IMetaTag.Row[]
  }

}
